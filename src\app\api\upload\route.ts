import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir, access } from 'fs/promises'
import { join } from 'path'
import { auth } from '@/auth'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const data = await request.formData()
    const file: File | null = data.get('file') as unknown as File

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'Only image files are allowed' }, { status: 400 })
    }

    // Validate file size (4MB limit)
    const maxSize = 4 * 1024 * 1024 // 4MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File size too large. Maximum 4MB allowed.' }, { status: 400 })
    }

    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'public', 'uploads')
    try {
      await mkdir(uploadsDir, { recursive: true })
    } catch (error) {
      // Directory might already exist
    }

    // Generate deterministic filename for thumbnails, or unique filename for other files
    let filename: string;
    const extension = file.name.split('.').pop() || 'jpg';

    // Check if this is a thumbnail upload (deterministic naming)
    if (file.name.startsWith('thumbnail-') && file.name.includes('.')) {
      // Use the provided filename for thumbnails (e.g., "thumbnail-{projectId}.jpg")
      filename = file.name;
    } else {
      // Generate unique filename for non-thumbnail uploads
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      filename = `${timestamp}-${randomString}.${extension}`;
    }
    
    const filepath = join(uploadsDir, filename)

    // For thumbnail files, check if file exists and log replacement
    if (file.name.startsWith('thumbnail-')) {
      try {
        await access(filepath);
        console.log(`🔄 Replacing existing thumbnail: ${filename}`);
      } catch {
        console.log(`📁 Creating new thumbnail: ${filename}`);
      }
    }

    // Write file (this will overwrite existing files)
    await writeFile(filepath, buffer)
    
    // Return the public URL
    const url = `/uploads/${filename}`
    
    return NextResponse.json({ 
      url,
      name: file.name,
      size: file.size,
      type: file.type
    })

  } catch (error) {
    // Upload error - logging removed
    return NextResponse.json({ error: 'Upload failed' }, { status: 500 })
  }
}

// Handle GET requests (for compatibility)
export async function GET() {
  return NextResponse.json({ message: 'Upload endpoint. Use POST to upload files.' })
}

#!/usr/bin/env node

/**
 * Development Optimization Script
 * Clears Next.js cache and optimizes for faster development
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Optimizing development environment...');

// Clear Next.js cache
const nextCacheDir = path.join(process.cwd(), '.next');
if (fs.existsSync(nextCacheDir)) {
  console.log('🗑️  Clearing Next.js cache...');
  try {
    execSync(`rm -rf "${nextCacheDir}"`, { stdio: 'inherit' });
    console.log('✅ Next.js cache cleared');
  } catch (error) {
    console.log('⚠️  Could not clear Next.js cache (this is normal on Windows)');
  }
}

// Clear node_modules cache (optional)
const nodeModulesCacheDir = path.join(process.cwd(), 'node_modules', '.cache');
if (fs.existsSync(nodeModulesCacheDir)) {
  console.log('🗑️  Clearing node_modules cache...');
  try {
    execSync(`rm -rf "${nodeModulesCacheDir}"`, { stdio: 'inherit' });
    console.log('✅ Node modules cache cleared');
  } catch (error) {
    console.log('⚠️  Could not clear node_modules cache');
  }
}

// Check for large files that might slow down compilation
console.log('📊 Checking for large files...');
const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
if (fs.existsSync(uploadsDir)) {
  const files = fs.readdirSync(uploadsDir);
  const largeFiles = files.filter(file => {
    const filePath = path.join(uploadsDir, file);
    const stats = fs.statSync(filePath);
    return stats.size > 1024 * 1024; // Files larger than 1MB
  });
  
  if (largeFiles.length > 0) {
    console.log(`⚠️  Found ${largeFiles.length} large files in uploads directory`);
    console.log('   Consider running: node cleanup-thumbnails.js');
  } else {
    console.log('✅ No large files found');
  }
}

console.log('');
console.log('🎯 Development optimization complete!');
console.log('');
console.log('💡 Tips for faster development:');
console.log('   • Use: npm run dev (with Turbopack)');
console.log('   • Keep browser dev tools closed when not needed');
console.log('   • Close unused browser tabs');
console.log('   • Use: npm run dev:webpack (fallback to Webpack)');
console.log('');
console.log('🚀 Ready to start development server!');

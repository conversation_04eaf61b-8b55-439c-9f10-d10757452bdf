// Utility to clear NextAuth cookies when JWT session errors occur
// This helps resolve "no matching decryption secret" errors

export function clearAuthCookies() {
  if (typeof window === 'undefined') return;
  
  // List of NextAuth cookie names to clear
  const authCookieNames = [
    'next-auth.session-token',
    '__Secure-next-auth.session-token',
    'next-auth.csrf-token',
    '__Host-next-auth.csrf-token',
    'next-auth.callback-url',
    '__Secure-next-auth.callback-url',
    'next-auth.pkce.code_verifier',
    '__Secure-next-auth.pkce.code_verifier'
  ];
  
  // Clear each cookie
  authCookieNames.forEach(cookieName => {
    // Clear for current domain
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    
    // Clear for localhost specifically
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=localhost;`;
    
    // Clear for current hostname
    if (window.location.hostname !== 'localhost') {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
    }
  });
  
  console.log('Auth cookies cleared. Please refresh the page.');
}

// Auto-clear cookies if JWT session error is detected
export function autoFixJWTError() {
  if (typeof window === 'undefined') return;
  
  // Listen for console errors that indicate JWT issues
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const errorMessage = args.join(' ');
    
    if (errorMessage.includes('JWTSessionError') || 
        errorMessage.includes('no matching decryption secret')) {
      console.warn('JWT session error detected. Clearing auth cookies...');
      clearAuthCookies();
      
      // Show user-friendly message
      if (window.confirm('Authentication session expired. Clear cookies and refresh the page?')) {
        window.location.reload();
      }
    }
    
    // Call original console.error
    originalConsoleError.apply(console, args);
  };
}

// Manual cookie clearing function for development
export function devClearCookies() {
  if (typeof window === 'undefined') return;
  
  clearAuthCookies();
  alert('Auth cookies cleared! The page will refresh.');
  window.location.reload();
}

// Add to window for easy access in browser console
if (typeof window !== 'undefined') {
  window.clearAuthCookies = devClearCookies;
}

"use client";

import { useEffect, useState, use<PERSON><PERSON>back, useMemo, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Loader2, ArrowLeft, Download, Upload, Type, Image as ImageIcon, Menu, X, Undo2, Redo2 } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { TemplateResponse, EditableLayer } from "@/types/template";
import dynamic from "next/dynamic";

const CustomizationEditor = dynamic(
  () => import("@/features/editor/components/customization-editor").then(mod => ({ default: mod.CustomizationEditor })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-full min-h-[400px]">
        <div className="flex flex-col items-center space-y-3 bg-white p-6 rounded-lg shadow-lg">
          <Loader2 className="h-10 w-10 animate-spin text-blue-600" />
          <div className="text-center">
            <p className="text-lg font-medium text-gray-900">Loading Editor</p>
            <p className="text-sm text-gray-600">Please wait while we initialize the editor...</p>
          </div>
        </div>
      </div>
    )
  }
);

export default function CustomizeTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const [template, setTemplate] = useState<TemplateResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [customizations, setCustomizations] = useState<Record<string, string>>({});
  // Optimized state management with useMemo and useCallback
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const [previewCache] = useState(() => new Map<string, string>());
  const lastCustomizationHash = useRef<string>('');
  const [isDownloading, setIsDownloading] = useState(false);
  const [activeLayerId, setActiveLayerId] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [aiEditPrompt, setAiEditPrompt] = useState<string>("");
  const [isAiEditing, setIsAiEditing] = useState(false);
  const [isQuickEditing, setIsQuickEditing] = useState(false);
  const [isCommanderEditing, setIsCommanderEditing] = useState(false);

  // Editor instance ref for undo/redo functionality
  const editorRef = useRef<any>(null);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  useEffect(() => {
    const fetchTemplate = async () => {
      try {
        const response = await fetch(`/api/projects/public/${params.projectId}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError("Template not found or not public");
          } else {
            setError("Failed to load template");
          }
          return;
        }

        const data = await response.json();
        const templateData = data.data;

        // Check if template is customizable
        if (!templateData.isCustomizable || !templateData.editableLayers) {
          setError("This template is not customizable");
          return;
        }

        // Parse editable layers safely
        let editableLayers: EditableLayer[] = [];
        try {
          editableLayers = templateData.editableLayers ? JSON.parse(templateData.editableLayers) : [];
        } catch (error) {
          console.error('Error parsing editable layers:', error);
          editableLayers = [];
        }

        console.log('Template loaded:', {
          templateData,
          editableLayers,
          json: templateData.json ? JSON.parse(templateData.json) : null
        });

        setTemplate({
          ...templateData,
          editableLayers,
        });

        // Initialize customizations with original values (only for text layers)
        const initialCustomizations: Record<string, string> = {};
        editableLayers.forEach(layer => {
          // Only set initial values for text layers, not images
          if (layer.type === 'text') {
            initialCustomizations[layer.id] = layer.originalValue || '';
          }
          // For images, leave empty so they use their original canvas state
        });
        console.log('Setting initial customizations:', initialCustomizations);
        setCustomizations(initialCustomizations);

      } catch (err) {
        setError("Failed to load template");
      } finally {
        setLoading(false);
      }
    };

    if (params.projectId) {
      fetchTemplate();
    }
  }, [params.projectId]);

  // Optimized preview generation with caching - moved up to avoid circular dependency
  const generateCustomizationHash = useCallback((customizations: Record<string, string>) => {
    return JSON.stringify(Object.entries(customizations).sort());
  }, []);

  // Optimized customization change handler with caching
  const handleCustomizationChange = useCallback((layerId: string, value: string) => {
    console.log('Customization change:', layerId, value);
    console.log('Value type:', typeof value, 'Length:', value?.length);

    // Check cache first to avoid unnecessary updates
    const newCustomizations = { ...customizations, [layerId]: value };
    const newHash = generateCustomizationHash(newCustomizations);

    // Use cached preview if available
    if (previewCache.has(newHash)) {
      const cachedPreview = previewCache.get(newHash)!;
      setPreviewUrl(cachedPreview);
    }

    setCustomizations(newCustomizations);
  }, [customizations, generateCustomizationHash, previewCache]);

  const handleImageUpload = async (layerId: string, file: File) => {
    console.log('Image upload started for layer:', layerId, file);

    try {
      // Comprehensive file validation
      if (!file.type.startsWith('image/')) {
        console.error('Invalid file type:', file.type);
        alert('Please upload a valid image file (JPG, PNG, GIF, WebP, etc.)');
        return;
      }

      // Check file size (limit to 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        console.error('File too large:', file.size);
        alert('Image file is too large. Please upload an image smaller than 10MB.');
        return;
      }

      // Check if file is actually readable
      if (file.size === 0) {
        console.error('Empty file detected');
        alert('The selected file appears to be empty. Please choose a different image.');
        return;
      }

      // Find the layer to check constraints
      const layer = template?.editableLayers?.find(l => l.id === layerId);
      if (layer?.constraints?.allowedFormats) {
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        const allowedFormats = layer.constraints.allowedFormats.map(f => f.toLowerCase());

        if (fileExtension && !allowedFormats.includes(fileExtension)) {
          console.error('File format not allowed:', fileExtension, 'Allowed:', allowedFormats);
          alert(`This layer only accepts ${allowedFormats.join(', ').toUpperCase()} files. Please upload a compatible image.`);
          return;
        }
      }

      // Create object URL for immediate preview
      const imageUrl = URL.createObjectURL(file);
      console.log('Created object URL:', imageUrl);

      // Validate that the image can be loaded
      await new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          console.log('Image validation successful:', {
            width: img.naturalWidth,
            height: img.naturalHeight,
            size: file.size,
            type: file.type
          });
          resolve();
        };
        img.onerror = () => {
          console.error('Image failed to load during validation');
          reject(new Error('Invalid or corrupted image file'));
        };
        img.src = imageUrl;
      });

      // Update customizations to trigger canvas update
      handleCustomizationChange(layerId, imageUrl);

      // Auto-select the layer
      handleLayerActivation(layerId);

      console.log('Image upload completed successfully');
    } catch (error) {
      console.error('Error handling image upload:', error);

      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload image';
      alert(`Upload failed: ${errorMessage}. Please try again with a different image.`);

      // Clean up any created object URLs
      try {
        if (typeof error === 'object' && error !== null && 'imageUrl' in error) {
          URL.revokeObjectURL(error.imageUrl as string);
        }
      } catch (cleanupError) {
        console.warn('Failed to cleanup object URL:', cleanupError);
      }
    }
  };

  const handlePreviewGenerated = useCallback((dataUrl: string) => {
    if (dataUrl) {
      const currentHash = generateCustomizationHash(customizations);
      previewCache.set(currentHash, dataUrl);
      lastCustomizationHash.current = currentHash;
    }
    setPreviewUrl(dataUrl);
  }, [customizations, generateCustomizationHash, previewCache]);

  const handleLayerActivation = (layerId: string | null) => {
    console.log('🎯 handleLayerActivation called with:', layerId);
    console.log('🎯 Previous activeLayerId:', activeLayerId);
    setActiveLayerId(layerId);
    console.log('🎯 Setting activeLayerId to:', layerId);
    // Close sidebar on mobile when a layer is selected
    if (typeof window !== 'undefined' && window.innerWidth < 1280) { // xl breakpoint
      setIsSidebarOpen(false);
    }
  };

  const handleAiEdit = async (layerId: string, prompt: string) => {
    if (!prompt.trim()) {
      alert('Please enter an editing prompt');
      return;
    }

    console.log('Dispatching AI edit event for layer:', layerId, 'with prompt:', prompt);
    setIsAiEditing(true);

    // Dispatch AI edit event to the CustomizationEditor component
    const event = new CustomEvent('aiEditImage', {
      detail: {
        layerId,
        prompt: prompt.trim()
      }
    });
    window.dispatchEvent(event);

    // Clear the prompt after dispatching
    setAiEditPrompt('');

    // Reset the loading state after a short delay (the editor will handle the actual processing)
    setTimeout(() => {
      setIsAiEditing(false);
    }, 1000);
  };

  const handleQuickEdit = async (layerId: string) => {
    const quickEditPrompt = "If the person is wearing a suit, change the suit color to yellow while keeping the existing shirt and tie. If the person is not wearing a suit, add a yellow suit jacket, white dress shirt, and yellow tie to make them appear professionally dressed in yellow business attire.";

    console.log('Dispatching quick edit event for layer:', layerId);
    setIsQuickEditing(true);

    // Dispatch AI edit event to the CustomizationEditor component with the predefined prompt
    const event = new CustomEvent('aiEditImage', {
      detail: {
        layerId,
        prompt: quickEditPrompt
      }
    });
    window.dispatchEvent(event);

    // Reset the loading state after a short delay (the editor will handle the actual processing)
    setTimeout(() => {
      setIsQuickEditing(false);
    }, 1000);
  };

  const handleCommanderEdit = async (layerId: string) => {
    const commanderEditPrompt = "Transform the person into wearing a formal, military-inspired yellow uniform with these specific details: Replace all clothing with a bright vibrant yellow long-sleeved military jacket featuring a standard pointed collar worn down, black epaulettes on both shoulders with three small white five-pointed stars arranged in a row from collar to shoulder seam. Add a yellow felt beret with a small black ring on the bottom worn slightly angled to one side, and ensure the uniform maintains a professional military appearance while keeping the person's face and pose unchanged.";

    console.log('Dispatching commander edit event for layer:', layerId);
    setIsCommanderEditing(true);

    // Dispatch AI edit event to the CustomizationEditor component with the predefined prompt
    const event = new CustomEvent('aiEditImage', {
      detail: {
        layerId,
        prompt: commanderEditPrompt
      }
    });
    window.dispatchEvent(event);

    // Reset the loading state after a short delay (the editor will handle the actual processing)
    setTimeout(() => {
      setIsCommanderEditing(false);
    }, 1000);
  };

  // Handle editor ready callback
  const handleEditorReady = useCallback((editor: any) => {
    console.log('Editor ready in public page:', editor);
    editorRef.current = editor;

    // Update undo/redo state
    if (editor) {
      setCanUndo(editor.canUndo?.() || false);
      setCanRedo(editor.canRedo?.() || false);

      // Listen for canvas changes to update undo/redo state
      if (editor.canvas) {
        const updateUndoRedoState = () => {
          setCanUndo(editor.canUndo?.() || false);
          setCanRedo(editor.canRedo?.() || false);
        };

        editor.canvas.on('path:created', updateUndoRedoState);
        editor.canvas.on('object:added', updateUndoRedoState);
        editor.canvas.on('object:removed', updateUndoRedoState);
        editor.canvas.on('object:modified', updateUndoRedoState);
      }
    }
  }, []);

  // Undo/Redo handlers
  const handleUndo = useCallback(() => {
    if (editorRef.current?.onUndo) {
      editorRef.current.onUndo();
      setCanUndo(editorRef.current.canUndo?.() || false);
      setCanRedo(editorRef.current.canRedo?.() || false);
    }
  }, []);

  const handleRedo = useCallback(() => {
    if (editorRef.current?.onRedo) {
      editorRef.current.onRedo();
      setCanUndo(editorRef.current.canUndo?.() || false);
      setCanRedo(editorRef.current.canRedo?.() || false);
    }
  }, []);

  // Initialize preview with template thumbnail
  useEffect(() => {
    if (template?.thumbnailUrl) {
      setPreviewUrl(template.thumbnailUrl);
    }
  }, [template]);

  // Close sidebar on window resize to desktop size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1280) { // xl breakpoint
        setIsSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const generatePreview = async () => {
    setIsGeneratingPreview(true);
    try {
      // Trigger preview generation from the canvas
      const event = new CustomEvent('generatePreview');
      window.dispatchEvent(event);

      // Small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (err) {
      console.error('Failed to generate preview:', err);
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  const downloadCustomized = async () => {
    // Download button clicked - logging removed
    setIsDownloading(true);
    try {
      // Generate a high-quality image from the current canvas state
      // This will be handled by the CustomizationEditor component
      const filename = `customized-${template?.name || 'design'}.png`;
      // Dispatching download event with filename - logging removed

      const event = new CustomEvent('downloadCustomized', {
        detail: {
          filename: filename,
          quality: 1.0,
          format: 'png'
        }
      });
      window.dispatchEvent(event);

      // Small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Download process completed - logging removed
    } catch (err) {
      // Failed to download - logging removed
    } finally {
      setIsDownloading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-purple-200 rounded-full animate-spin"></div>
            <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-purple-600 rounded-full animate-spin"></div>
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-800">Loading Template Editor</h3>
            <p className="text-sm text-gray-600">Preparing your customization workspace...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !template) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto px-4">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
            <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">!</span>
            </div>
          </div>
          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-gray-900">
              {error || "Template not found"}
            </h1>
            <p className="text-gray-600">
              We couldn&apos;t load the template you&apos;re looking for. Please try again or go back to browse other templates.
            </p>
          </div>
          <Button
            onClick={() => router.push("/")}
            variant="outline"
            className="border-gray-300 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Gallery
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50 flex flex-col">
      {/* Enhanced Header */}
      <div className="bg-yellow-100/80 backdrop-blur-md border-b border-yellow-200/50 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              {/* Mobile Menu Button */}
              <div className="relative xl:hidden">
                <Button
                  onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                  variant="ghost"
                  size="sm"
                  className="hover:bg-gray-100/80 transition-all duration-200 hover:scale-105"
                  title={isSidebarOpen ? "Close tools" : "Open customization tools"}
                >
                  {isSidebarOpen ? (
                    <X className="h-4 w-4 transition-transform duration-200" />
                  ) : (
                    <Menu className="h-4 w-4 transition-transform duration-200" />
                  )}
                </Button>
                {!isSidebarOpen && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-purple-500 rounded-full animate-pulse" />
                )}
              </div>
              <Button
                onClick={() => router.push("/")}
                variant="ghost"
                size="sm"
                className="hover:bg-gray-100/80 transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Gallery
              </Button>
              <div className="h-6 w-px bg-gray-300 hidden sm:block" />
              <div>
                <h1 className="text-xl font-bold text-gray-900 bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                  Customize: {template.name}
                </h1>
                <p className="text-sm text-gray-600">
                  Make this template your own with our powerful editor
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={generatePreview}
                disabled={isGeneratingPreview}
                variant="outline"
                className="border-gray-300 hover:border-yellow-300 hover:bg-yellow-50 transition-all duration-200"
              >
                {isGeneratingPreview ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <ImageIcon className="h-4 w-4 mr-2" />
                )}
                Preview
              </Button>

              {/* Undo/Redo Buttons */}
              <div className="flex items-center space-x-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleUndo}
                  disabled={!canUndo}
                  className="border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-all duration-200"
                  title="Undo"
                >
                  <Undo2 className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRedo}
                  disabled={!canRedo}
                  className="border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-all duration-200"
                  title="Redo"
                >
                  <Redo2 className="h-4 w-4" />
                </Button>
              </div>

              <Button
                onClick={downloadCustomized}
                disabled={isDownloading}
                className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              >
                {isDownloading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Download
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 xl:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Mobile Sidebar */}
      <div className={`
        fixed top-0 left-0 h-full w-80 bg-white z-50 transform transition-transform duration-300 ease-in-out xl:hidden shadow-2xl
        ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-bold text-gray-800 flex items-center">
            <Type className="h-5 w-5 mr-2 text-yellow-600" />
            Customization Tools
          </h2>
          <Button
            onClick={() => setIsSidebarOpen(false)}
            variant="ghost"
            size="sm"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="p-4 space-y-4 max-h-[calc(100vh-80px)] overflow-y-auto">
          <p className="text-sm text-gray-600 mb-4">
            Click on elements in the editor to select and customize them
          </p>
          {template.editableLayers.map((layer) => (
            <div
              key={layer.id}
              className={`space-y-3 p-4 rounded-xl border-2 transition-all duration-200 cursor-pointer hover:shadow-md ${
                activeLayerId === layer.id
                  ? 'border-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 shadow-lg transform scale-[1.02]'
                  : 'border-gray-200 hover:border-yellow-200 bg-white hover:bg-yellow-50'
              }`}
              onClick={() => {
                console.log('Clicking layer:', layer.id, 'Current active:', activeLayerId);
                if (activeLayerId === layer.id) {
                  handleLayerActivation(null); // Deselect if already active
                } else {
                  handleLayerActivation(layer.id); // Select layer
                }
              }}
              data-preload="customization"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    layer.type === 'text'
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-green-100 text-green-600'
                  }`}>
                    {layer.type === 'text' ? (
                      <Type className="h-4 w-4" />
                    ) : (
                      <ImageIcon className="h-4 w-4" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 text-sm">
                      {layer.name}
                    </h3>
                    <Badge
                      variant="secondary"
                      className={`text-xs ${
                        layer.type === 'text'
                          ? 'bg-blue-50 text-blue-700 border-blue-200'
                          : 'bg-green-50 text-green-700 border-green-200'
                      }`}
                    >
                      {layer.type}
                    </Badge>
                  </div>
                </div>
                {activeLayerId === layer.id && (
                  <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse" />
                )}
              </div>

              {/* Layer Controls */}
              {activeLayerId === layer.id && (
                <div className="space-y-3 pt-3 border-t border-gray-100">
                  {layer.type === 'text' && (
                    <div className="space-y-2">
                      <Label htmlFor={`text-${layer.id}`} className="text-sm font-medium text-gray-700">
                        Text Content
                      </Label>
                      <Input
                        id={`text-${layer.id}`}
                        value={customizations[layer.id] || ''}
                        onChange={(e) => handleCustomizationChange(layer.id, e.target.value)}
                        placeholder={`Enter ${layer.name.toLowerCase()}...`}
                        className="text-sm border-gray-300 focus:border-yellow-400 focus:ring-yellow-400"
                      />
                    </div>
                  )}

                  {layer.type === 'image' && (
                    <div className="space-y-3">
                      <Label htmlFor={`image-upload-${layer.id}`} className="text-sm font-medium text-gray-700">
                        Replace Image
                      </Label>
                      <div className="flex flex-col space-y-2">
                        <Input
                          id={`image-upload-${layer.id}`}
                          name={`image-upload-${layer.id}`}
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              // Use the improved handleImageUpload function for consistency
                              handleImageUpload(layer.id, file);
                            }
                          }}
                          className="text-sm border-gray-300 focus:border-yellow-400 focus:ring-yellow-400"
                        />
                        <p className="text-xs text-gray-500">
                          Upload a new image to replace the current one
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Enhanced Content */}
      <div className="container mx-auto px-4 py-6 flex-1">
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-4 lg:gap-6 h-full max-w-full">
          {/* Desktop Customization Panel */}
          <div className="xl:col-span-1 order-2 xl:order-1 hidden xl:block">
            <Card className="h-full shadow-xl border-0 bg-white/90 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-yellow-50 to-orange-50 border-b border-gray-100">
                <CardTitle className="text-lg font-bold text-gray-800 flex items-center">
                  <Type className="h-5 w-5 mr-2 text-yellow-600" />
                  Customization Tools
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Click on elements in the editor to select and customize them
                </p>
              </CardHeader>
              <CardContent className="space-y-4 max-h-[calc(100vh-300px)] overflow-y-auto">
                {template.editableLayers.map((layer) => (
                  <div
                    key={layer.id}
                    className={`space-y-3 p-4 rounded-xl border-2 transition-all duration-200 cursor-pointer hover:shadow-md ${
                      activeLayerId === layer.id
                        ? 'border-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 shadow-lg transform scale-[1.02]'
                        : 'border-gray-200 hover:border-yellow-200 bg-white hover:bg-yellow-50'
                    }`}
                    onClick={() => {
                      console.log('Clicking layer:', layer.id, 'Current active:', activeLayerId);
                      if (activeLayerId === layer.id) {
                        handleLayerActivation(null); // Deselect if already active
                      } else {
                        handleLayerActivation(layer.id); // Select layer
                      }
                    }}
                    data-preload="customization"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${
                          layer.type === 'text'
                            ? 'bg-blue-100 text-blue-600'
                            : 'bg-green-100 text-green-600'
                        }`}>
                          {layer.type === 'text' ? (
                            <Type className="h-4 w-4" />
                          ) : (
                            <ImageIcon className="h-4 w-4" />
                          )}
                        </div>
                        <div>
                          <span className="font-semibold text-gray-900 block">
                            {layer.name}
                          </span>
                          <span className="text-xs text-gray-500">
                            {layer.type === 'text' ? 'Text Element' : 'Image Element'}
                          </span>
                        </div>
                      </div>
                      <Badge
                        variant="outline"
                        className={`text-xs font-medium ${
                          activeLayerId === layer.id
                            ? 'border-purple-300 text-purple-700 bg-purple-50'
                            : 'border-gray-300 text-gray-600'
                        }`}
                      >
                        {layer.type}
                      </Badge>
                    </div>
                    {activeLayerId === layer.id && (
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                        <span className="text-xs text-purple-600 font-medium">Active</span>
                      </div>
                    )}

                    {layer.type === 'text' ? (
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700 flex items-center">
                          <Type className="h-3 w-3 mr-1" />
                          {layer.placeholder || 'Enter text'}
                        </Label>
                        <Input
                          value={customizations[layer.id] || (layer.type === 'text' ? layer.originalValue : '') || ''}
                          onChange={(e) => handleCustomizationChange(layer.id, e.target.value)}
                          placeholder={layer.placeholder}
                          maxLength={layer.constraints?.maxLength}
                          className={`transition-all duration-200 ${
                            activeLayerId === layer.id
                              ? 'border-purple-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-200 bg-purple-50/50'
                              : 'border-gray-300 focus:border-gray-400 hover:border-gray-400'
                          }`}
                        />
                        {activeLayerId === layer.id && (
                          <div className="flex items-center space-x-2 p-2 bg-purple-50 rounded-lg border border-purple-200">
                            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                            <p className="text-xs text-purple-700 font-medium">
                              Selected - Type here or double-click in editor
                            </p>
                          </div>
                        )}
                        {layer.constraints?.maxLength && (
                          <div className="flex justify-between items-center">
                            <p className="text-xs text-gray-500">
                              Character count
                            </p>
                            <p className={`text-xs font-medium ${
                              (customizations[layer.id]?.length || 0) > (layer.constraints.maxLength * 0.8)
                                ? 'text-orange-600'
                                : 'text-gray-600'
                            }`}>
                              {customizations[layer.id]?.length || 0}/{layer.constraints.maxLength}
                            </p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Label htmlFor={`image-upload-${layer.id}`} className="text-sm font-medium text-gray-700 flex items-center">
                          <ImageIcon className="h-3 w-3 mr-1" />
                          Upload your image
                        </Label>
                        <div className="space-y-2">
                          <div className="relative">
                            <input
                              id={`image-upload-${layer.id}`}
                              name={`image-upload-${layer.id}`}
                              type="file"
                              accept={layer.constraints?.allowedFormats?.map(f => `.${f}`).join(',') || 'image/*'}
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) {
                                  handleImageUpload(layer.id, file);
                                  handleLayerActivation(layer.id); // Auto-select when uploading
                                }
                              }}
                              className="hidden"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => document.getElementById(`image-upload-${layer.id}`)?.click()}
                              className={`w-full h-8 text-xs ${
                                activeLayerId === layer.id
                                  ? 'border-purple-400 text-purple-700 bg-purple-50'
                                  : 'border-gray-300 text-gray-600 hover:border-gray-400'
                              }`}
                            >
                              <ImageIcon className="h-3 w-3 mr-1" />
                              Choose File
                            </Button>
                          </div>
                          <p className="text-xs text-gray-500">
                            {layer.constraints?.allowedFormats?.join(', ').toUpperCase() || 'PNG, JPG, GIF'} up to 10MB
                          </p>

                          {activeLayerId === layer.id && (
                            <div className="flex items-center space-x-2 p-1.5 bg-purple-50 rounded border border-purple-200">
                              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                              <p className="text-xs text-purple-700 font-medium">
                                Selected - Choose file to replace
                              </p>
                            </div>
                          )}



                          {/* AI Edit Tool - Show when layer is active (will be disabled if no image) */}
                          {activeLayerId === layer.id && (
                            <div className="space-y-2 pt-2 border-t border-gray-200" onClick={(e) => e.stopPropagation()}>
                              <Label htmlFor={`ai-edit-${layer.id}`} className="text-sm font-medium text-gray-700 flex items-center">
                                <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                AI Edit
                              </Label>
                              <div className="space-y-2" onClick={(e) => e.stopPropagation()}>
                                <Input
                                  id={`ai-edit-${layer.id}`}
                                  type="text"
                                  placeholder="Describe how to edit the image..."
                                  value={aiEditPrompt}
                                  onChange={(e) => setAiEditPrompt(e.target.value)}
                                  disabled={isAiEditing}
                                  className="text-xs h-8 border-gray-300 focus:border-purple-400 focus:ring-purple-400"
                                  onClick={(e) => e.stopPropagation()}
                                  onFocus={(e) => e.stopPropagation()}
                                  onKeyDown={(e) => {
                                    e.stopPropagation();
                                    if (e.key === 'Enter' && !isAiEditing && aiEditPrompt.trim()) {
                                      handleAiEdit(layer.id, aiEditPrompt);
                                    }
                                  }}
                                />
                                <div className="space-y-2">
                                  <Button
                                    type="button"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleAiEdit(layer.id, aiEditPrompt);
                                    }}
                                    disabled={isAiEditing || isQuickEditing || isCommanderEditing || !aiEditPrompt.trim() || layer.type !== 'image'}
                                    className="w-full h-8 text-xs bg-purple-600 hover:bg-purple-700 text-white disabled:bg-gray-400"
                                  >
                                    {isAiEditing ? (
                                      <>
                                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                        Generating...
                                      </>
                                    ) : (
                                      <>
                                        <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                        </svg>
                                        Generate
                                      </>
                                    )}
                                  </Button>

                                  <Button
                                    type="button"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleQuickEdit(layer.id);
                                    }}
                                    disabled={isAiEditing || isQuickEditing || isCommanderEditing || layer.type !== 'image'}
                                    className="w-full h-8 text-xs bg-yellow-600 hover:bg-yellow-700 text-white disabled:bg-gray-400 transition-colors"
                                  >
                                    {isQuickEditing ? (
                                      <>
                                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                        Processing...
                                      </>
                                    ) : (
                                      <>
                                        <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                        Quick Edit: Yellow Suit
                                      </>
                                    )}
                                  </Button>

                                  <Button
                                    type="button"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleCommanderEdit(layer.id);
                                    }}
                                    disabled={isAiEditing || isQuickEditing || isCommanderEditing || layer.type !== 'image'}
                                    className="w-full h-8 text-xs bg-green-700 hover:bg-green-800 text-white disabled:bg-gray-400 transition-colors"
                                  >
                                    {isCommanderEditing ? (
                                      <>
                                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                        Processing...
                                      </>
                                    ) : (
                                      <>
                                        <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                        </svg>
                                        Quick Edit: NRM Commander
                                      </>
                                    )}
                                  </Button>
                                </div>
                                <p className="text-xs text-gray-500">
                                  Use AI to edit your image with natural language prompts, or try quick edits for instant transformations: business attire or military uniform
                                </p>
                              </div>
                            </div>
                          )}

                          {/* Enhanced preview of uploaded image */}
                          {customizations[layer.id] && (
                            <div className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200 shadow-sm">
                              <img
                                src={customizations[layer.id]}
                                alt="Uploaded preview"
                                className="w-16 h-16 object-cover rounded-lg border border-gray-200"
                              />
                              <div className="flex-1 min-w-0">
                                <p className="text-xs text-gray-600">Image uploaded</p>
                                <button
                                  onClick={() => {
                                    setCustomizations(prev => {
                                      const updated = { ...prev };
                                      delete updated[layer.id];
                                      return updated;
                                    });
                                  }}
                                  className="text-xs text-red-500 hover:text-red-700"
                                >
                                  Remove
                                </button>
                              </div>
                            </div>
                          )}

                          {layer.constraints?.maxFileSize && (
                            <p className="text-xs text-gray-500">
                              Max size: {Math.round(layer.constraints.maxFileSize / 1024 / 1024)}MB
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Editor Panel */}
          <div className="xl:col-span-3 order-1 xl:order-2 col-span-full xl:col-span-3">
            <Card className="h-full w-full shadow-xl border-0 bg-white/90 backdrop-blur-sm flex flex-col">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100 pb-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <div>
                    <CardTitle className="text-lg font-bold text-gray-800 flex items-center">
                      <div className="p-2 bg-blue-100 rounded-lg mr-3">
                        <ImageIcon className="h-5 w-5 text-blue-600" />
                      </div>
                      Template Editor
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">
                      Click to select elements • Double-click text to edit • Use toolbar for advanced features
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1 px-3 py-1 bg-green-100 rounded-full">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-xs font-medium text-green-700">Live Editor</span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="flex-1 p-1 bg-gray-50/50 min-h-0">
                <CustomizationEditor
                  templateData={{
                    id: template.id,
                    name: template.name,
                    width: template.width,
                    height: template.height,
                    json: template.json,
                    editableLayers: template.editableLayers,
                  }}
                  customizations={customizations}
                  onCustomizationChange={handleCustomizationChange}
                  onPreviewGenerated={handlePreviewGenerated}
                  activeLayerId={activeLayerId}
                  onLayerActivation={handleLayerActivation}
                  onEditorReady={handleEditorReady}
                  isPublicCustomization={true}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Enhanced Footer */}
      <div className="bg-yellow-100/80 backdrop-blur-md border-t border-yellow-200/50 mt-8">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
              <div className="text-sm text-gray-600">
                <span className="font-medium">Template:</span> {template.name}
              </div>
              <div className="hidden sm:block w-px h-4 bg-gray-300"></div>
              <div className="text-sm text-gray-600">
                <span className="font-medium">Layers:</span> {template.editableLayers.length} editable
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Auto-save enabled</span>
              </div>
              <div className="hidden md:block">
                Press <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Ctrl+Z</kbd> to undo
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

# Clear Authentication Cookies

If you're seeing JWT session errors like "no matching decryption secret", you need to clear the browser cookies that were encrypted with the old AUTH_SECRET.

## Quick Fix - Clear Cookies

### Method 1: Clear All Cookies for localhost
1. Open browser Developer Tools (F12)
2. Go to **Application** tab (Chrome) or **Storage** tab (Firefox)
3. Find **Cookies** in the left sidebar
4. Click on `http://localhost:3003` (or whatever port you're using)
5. Delete all cookies, especially:
   - `next-auth.session-token`
   - `__Secure-next-auth.session-token`
   - `next-auth.csrf-token`
   - `__Host-next-auth.csrf-token`

### Method 2: Incognito/Private Window
- Open an incognito/private browser window
- Navigate to http://localhost:3003
- This will start with fresh cookies

### Method 3: Clear Site Data
1. In Chrome: Settings > Privacy and Security > Site Settings
2. Find localhost:3003 in the list
3. Click "Clear data"

## What Fixed the Issue

✅ **Updated AUTH_SECRET**: Generated a new secure 32-byte base64 secret
✅ **Fixed environment variable access**: Changed from `c.env.AUTH_SECRET` to `process.env.AUTH_SECRET`
✅ **Restarted server**: New AUTH_SECRET is now loaded

## Current Server Status

- **Server URL**: http://localhost:3003
- **AUTH_SECRET**: Updated with secure generated key
- **Status**: Ready for testing

## Test Authentication

1. Clear cookies using one of the methods above
2. Navigate to http://localhost:3003
3. Try signing in with credentials or OAuth providers
4. JWT session errors should be resolved

## If Issues Persist

If you still see JWT errors:
1. Make sure the server restarted after updating AUTH_SECRET
2. Verify .env.local has the new AUTH_SECRET value
3. Clear all browser data for localhost
4. Try a different browser or incognito mode

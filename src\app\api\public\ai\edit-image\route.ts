import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { generateImageVariations } from "@/services/aiService";

const editImageSchema = z.object({
  image: z.string(),
  prompt: z.string(),
  model: z.string().optional(),
  width: z.number().optional(),
  height: z.number().optional(),
  steps: z.number().optional(),
  guidance: z.number().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { image, prompt, model, width, height, steps, guidance } = editImageSchema.parse(body);

    console.log('Public AI edit request:', {
      hasImage: !!image,
      prompt: prompt.substring(0, 50) + '...',
      model: model || 'default'
    });

    // Call the AI service to generate image variations (which acts as editing)
    const result = await generateImageVariations(image, prompt, {
      model: model || 'black-forest-labs/FLUX.1-dev',
      width: width || 1024,
      height: height || 1024,
      steps: steps || 20,
      guidance: guidance || 7.5
    });

    console.log('AI edit successful:', {
      resultUrl: result ? result.substring(0, 50) + '...' : 'none'
    });

    return NextResponse.json({ data: result });
  } catch (error) {
    console.error('Public AI edit failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: `AI editing failed: ${errorMessage}` },
      { status: 500 }
    );
  }
}

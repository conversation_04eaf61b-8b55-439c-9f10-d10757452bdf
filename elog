 Warning: Extra attributes from the server: webcrx 
overrideMethod @ installHook.js:1
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 Template loaded: Object
 Setting initial customizations: Object
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 Template loaded: Object
 Setting initial customizations: Object
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 🔒 PUBLIC MODE: Creating isolated template copy to prevent original template modification
 🔒 PUBLIC MODE: Creating isolated template copy to prevent original template modification
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 [Fast Refresh] rebuilding
 🔒 PUBLIC MODE: Creating isolated template copy to prevent original template modification
 🔒 PUBLIC MODE: Creating isolated template copy to prevent original template modification
 🔒 PUBLIC MODE: Creating isolated template copy to prevent original template modification
 🔒 PUBLIC MODE: Creating isolated template copy to prevent original template modification
 [Fast Refresh] rebuilding
 🔒 PUBLIC CUSTOMIZATION MODE ACTIVE - All changes are temporary and will NOT affect the original template
 Canvas init attempt 1/10: Object
 Canvas initialized with dimensions: 1012 x 506
 Setting up event listeners for download and preview
 🚫 No editor canvas available for selection
 Cleaning up event listeners
 🔒 PUBLIC CUSTOMIZATION MODE ACTIVE - All changes are temporary and will NOT affect the original template
 Canvas init attempt 1/10: Object
 Canvas initialized with dimensions: 1012 x 506
 Setting up event listeners for download and preview
 🚫 No editor canvas available for selection
 Cleaning up event listeners
 useLoadState: Attempting to load initial state
 useLoadState: Parsed JSON data: Object
 useLoadState: Canvas dimensions: Object
 useLoadState: Loading JSON into canvas...
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
The provided value 'alphabetical' is not a valid enum value of type CanvasTextBaseline.
 Editor ready in public page: Object
 Editor is ready, checking for content
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 ⏳ Canvas still loading, deferring selection
 useLoadState: JSON loaded successfully, objects count: 6
 useLoadState: Calling autoZoom...
 useLoadState: Template loading complete
 Cleaning up event listeners
 Editor ready in public page: Object
 Editor is ready, checking for content
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 ⏳ Canvas still loading, deferring selection
 Cleaning up event listeners
 Editor ready in public page: Object
 Editor is ready, checking for content
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 ⏳ Canvas still loading, deferring selection
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753675387044 (text): Heading
 Skipping layer layer_1753675387044 - no custom value or same as original
 Processing layer layer_1753675393572 (image): undefined
 Skipping layer layer_1753675393572 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753675387044 (text): Heading
 Skipping layer layer_1753675387044 - no custom value or same as original
 Processing layer layer_1753675393572 (image): undefined
 Skipping layer layer_1753675393572 - no custom value or same as original
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753675387044 (text): Heading
 Skipping layer layer_1753675387044 - no custom value or same as original
 Processing layer layer_1753675393572 (image): undefined
 Skipping layer layer_1753675393572 - no custom value or same as original
 Canvas has 6 objects, checking for content...
 Canvas has content, clearing loading state
 Cleaning up event listeners
 Editor ready in public page: Object
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 Clearing canvas selection
 Canvas has 6 objects, checking for content...
 Canvas has content, clearing loading state
 Canvas has 6 objects, checking for content...
 Canvas has content, clearing loading state
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: polygon (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: polygon (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 Applying customizations: Object
 Template editable layers: Array(2)
 Processing layer layer_1753675387044 (text): Heading
 Skipping layer layer_1753675387044 - no custom value or same as original
 Processing layer layer_1753675393572 (image): undefined
 Skipping layer layer_1753675393572 - no custom value or same as original
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔒 Re-locked object: polygon (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 🔒 Re-locked object: textbox (ID: undefined)
 ✅ Assigning ID layer_1753675387044 to text object: Object
 Trying to match image layer layer_1753675393572 (Image 2) with 2 available image objects
 Available image objects: Array(2)
 Using stored properties for matching layer layer_1753675393572: Object
 Matched image by exact position/size for layer layer_1753675393572
 ✅ Assigning ID layer_1753675393572 to image object: Object
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
 🔒 Locked non-editable object: polygon (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
 🔒 Locked non-editable object: textbox (ID: no-id)
 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
 🔒 Locked non-editable object: polygon (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
 🔒 Locked non-editable object: textbox (ID: no-id)
 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
 🔒 Locked non-editable object: polygon (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
 🔒 Locked non-editable object: textbox (ID: no-id)
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
 🔒 Re-locked object: polygon (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
 🔒 Re-locked object: textbox (ID: undefined)
 ID Assignment Verification: Object
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
 🔒 Re-locked object: polygon (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
 🔒 Re-locked object: textbox (ID: undefined)
 ID Assignment Verification: Object
 Cleaning up event listeners
 Editor ready in public page: Object
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: Array(2)
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
customization-editor.tsx:2179 Clearing canvas selection
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2049 ID Assignment Verification: Object
customization-editor.tsx:2074 Template loaded and IDs assigned, clearing loading state
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
customization-editor.tsx:2001 Locking non-editable objects...
customization-editor.tsx:2021 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2021 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
customization-editor.tsx:2021 🔒 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
customization-editor.tsx:2021 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:2074 Template loaded and IDs assigned, clearing loading state
customization-editor.tsx:2074 Template loaded and IDs assigned, clearing loading state
customization-editor.tsx:1291 Applying customizations: Object
customization-editor.tsx:1292 Template editable layers: Array(2)
customization-editor.tsx:1296 Processing layer layer_1753675387044 (text): Heading
customization-editor.tsx:1300 Skipping layer layer_1753675387044 - no custom value or same as original
customization-editor.tsx:1296 Processing layer layer_1753675393572 (image): undefined
customization-editor.tsx:1300 Skipping layer layer_1753675393572 - no custom value or same as original
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2049 ID Assignment Verification: Object
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
customization-editor.tsx:2001 Locking non-editable objects...
customization-editor.tsx:2021 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2021 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
customization-editor.tsx:2021 🔒 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
customization-editor.tsx:2021 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2049 ID Assignment Verification: Object
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
hot-reloader-client.tsx:297 [Fast Refresh] rebuilding
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
page.tsx:240 🎯 handleLayerActivation called with: layer_1753675393572
page.tsx:241 🎯 Previous activeLayerId: null
page.tsx:243 🎯 Setting activeLayerId to: layer_1753675393572
customization-editor.tsx:1824 Cleaning up event listeners
page.tsx:321 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
customization-editor.tsx:1818 Setting up event listeners for download and preview
customization-editor.tsx:1838 Setting up canvas object IDs for editable layers: (2) ['layer_1753675387044', 'layer_1753675393572']
customization-editor.tsx:2096 🔥 External active layer changed: layer_1753675393572
customization-editor.tsx:2097 🔥 Editor canvas available: true
customization-editor.tsx:2098 🔥 Canvas objects count: 6
customization-editor.tsx:2104 All canvas objects: (6) [{…}, {…}, {…}, {…}, {…}, {…}]
customization-editor.tsx:2107 🔍 Looking for object with ID: layer_1753675393572
customization-editor.tsx:2108 🔍 Found target object: klass {filters: Array(0), cacheKey: 'texture5', type: 'image', version: '5.3.0', originX: 'center', …}
customization-editor.tsx:2109 🔍 Object details: {id: 'layer_1753675393572', type: 'image', left: 1037.09, top: 546.98}
customization-editor.tsx:2117 Selecting object in canvas: layer_1753675393572
customization-editor.tsx:1824 Cleaning up event listeners
page.tsx:321 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
customization-editor.tsx:1818 Setting up event listeners for download and preview
customization-editor.tsx:1838 Setting up canvas object IDs for editable layers: (2) ['layer_1753675387044', 'layer_1753675393572']
customization-editor.tsx:2096 🔥 External active layer changed: layer_1753675393572
customization-editor.tsx:2097 🔥 Editor canvas available: true
customization-editor.tsx:2098 🔥 Canvas objects count: 6
customization-editor.tsx:2104 All canvas objects: (6) [{…}, {…}, {…}, {…}, {…}, {…}]
customization-editor.tsx:2107 🔍 Looking for object with ID: layer_1753675393572
customization-editor.tsx:2108 🔍 Found target object: klass {filters: Array(0), cacheKey: 'texture5', type: 'image', version: '5.3.0', originX: 'center', …}
customization-editor.tsx:2109 🔍 Object details: {id: 'layer_1753675393572', type: 'image', left: 1037.09, top: 546.98}
customization-editor.tsx:2117 Selecting object in canvas: layer_1753675393572
customization-editor.tsx:1291 Applying customizations: {layer_1753675387044: 'Heading'}
customization-editor.tsx:1292 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:1296 Processing layer layer_1753675387044 (text): Heading
customization-editor.tsx:1300 Skipping layer layer_1753675387044 - no custom value or same as original
customization-editor.tsx:1296 Processing layer layer_1753675393572 (image): undefined
customization-editor.tsx:1300 Skipping layer layer_1753675393572 - no custom value or same as original
customization-editor.tsx:1291 Applying customizations: {layer_1753675387044: 'Heading'}
customization-editor.tsx:1292 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:1296 Processing layer layer_1753675387044 (text): Heading
customization-editor.tsx:1300 Skipping layer layer_1753675387044 - no custom value or same as original
customization-editor.tsx:1296 Processing layer layer_1753675393572 (image): undefined
customization-editor.tsx:1300 Skipping layer layer_1753675393572 - no custom value or same as original
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
customization-editor.tsx:2001 Locking non-editable objects...
customization-editor.tsx:2021 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2021 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
customization-editor.tsx:2021 🔒 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
customization-editor.tsx:2021 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
customization-editor.tsx:2001 Locking non-editable objects...
customization-editor.tsx:2021 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2021 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
customization-editor.tsx:2021 🔒 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
customization-editor.tsx:2021 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2049 ID Assignment Verification: {expected: Array(2), assigned: Array(2), missing: Array(0)}
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2049 ID Assignment Verification: {expected: Array(2), assigned: Array(2), missing: Array(0)}
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
page.tsx:240 🎯 handleLayerActivation called with: layer_1753675393572
page.tsx:241 🎯 Previous activeLayerId: layer_1753675393572
page.tsx:243 🎯 Setting activeLayerId to: layer_1753675393572
page.tsx:151 Image upload started for layer: layer_1753675393572 File {name: 'change_this_to_a_profesional_linkedin_image.png', lastModified: 1753191323252, lastModifiedDate: Tue Jul 22 2025 16:35:23 GMT+0300 (East Africa Time), webkitRelativePath: '', size: 1847135, …}
page.tsx:187 Created object URL: blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
page.tsx:134 Customization change: layer_1753675393572 blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
page.tsx:135 Value type: string Length: 63
page.tsx:240 🎯 handleLayerActivation called with: layer_1753675393572
page.tsx:241 🎯 Previous activeLayerId: layer_1753675393572
page.tsx:243 🎯 Setting activeLayerId to: layer_1753675393572
page.tsx:213 Image upload completed successfully
customization-editor.tsx:1824 Cleaning up event listeners
page.tsx:321 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
customization-editor.tsx:1818 Setting up event listeners for download and preview
customization-editor.tsx:1838 Setting up canvas object IDs for editable layers: (2) ['layer_1753675387044', 'layer_1753675393572']
customization-editor.tsx:2096 🔥 External active layer changed: layer_1753675393572
customization-editor.tsx:2097 🔥 Editor canvas available: true
customization-editor.tsx:2098 🔥 Canvas objects count: 6
customization-editor.tsx:2104 All canvas objects: (6) [{…}, {…}, {…}, {…}, {…}, {…}]
customization-editor.tsx:2107 🔍 Looking for object with ID: layer_1753675393572
customization-editor.tsx:2108 🔍 Found target object: klass {filters: Array(0), cacheKey: 'texture5', type: 'image', version: '5.3.0', originX: 'center', …}
customization-editor.tsx:2109 🔍 Object details: {id: 'layer_1753675393572', type: 'image', left: 1037.09, top: 546.98}
customization-editor.tsx:2117 Selecting object in canvas: layer_1753675393572
page.tsx:199 Image validation successful: {width: 1408, height: 792, size: 1847135, type: 'image/png'}
customization-editor.tsx:1291 Applying customizations: {layer_1753675387044: 'Heading', layer_1753675393572: 'blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9'}
customization-editor.tsx:1292 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:1296 Processing layer layer_1753675387044 (text): Heading
customization-editor.tsx:1300 Skipping layer layer_1753675387044 - no custom value or same as original
customization-editor.tsx:1296 Processing layer layer_1753675393572 (image): blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
customization-editor.tsx:1355 Replacing image for layer_1753675393572 with: blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
customization-editor.tsx:1356 Custom value type: string, starts with {: false
customization-editor.tsx:1419 Original image object: klass {filters: Array(0), cacheKey: 'texture5', type: 'image', version: '5.3.0', originX: 'center', …}
customization-editor.tsx:1420 New image loaded: klass {filters: Array(0), cacheKey: 'texture7', crossOrigin: 'anonymous', _element: img.canvas-img, _originalElement: img.canvas-img, …}
customization-editor.tsx:1425 Preserving original properties: {left: 1037.09, top: 546.98, width: 1080, height: 720, scaleX: 0.43, …}
customization-editor.tsx:82 🔍 Selected image boundary scaling: {selectedImageBoundaries: {…}, newImage: {…}, scales: {…}, result: {…}, behavior: 'SCALING DOWN (large image)', …}
customization-editor.tsx:1453 ✅ AI Edit scaling completed: {selectedImageBoundaries: {…}, newImageSize: {…}, finalScale: '0.330', resultSize: {…}, scalingBehavior: 'SCALING DOWN (large image)', …}
customization-editor.tsx:207 Applied scaling to new image: {originalScale: {…}, newScale: {…}, position: {…}}
customization-editor.tsx:1484 Replacing image at index 2
page.tsx:240 🎯 handleLayerActivation called with: null
page.tsx:241 🎯 Previous activeLayerId: layer_1753675393572
page.tsx:243 🎯 Setting activeLayerId to: null
customization-editor.tsx:1228 Canvas selection cleared
page.tsx:240 🎯 handleLayerActivation called with: null
page.tsx:241 🎯 Previous activeLayerId: layer_1753675393572
page.tsx:243 🎯 Setting activeLayerId to: null
customization-editor.tsx:1824 Cleaning up event listeners
page.tsx:321 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
customization-editor.tsx:1818 Setting up event listeners for download and preview
customization-editor.tsx:1838 Setting up canvas object IDs for editable layers: (2) ['layer_1753675387044', 'layer_1753675393572']
customization-editor.tsx:2096 🔥 External active layer changed: null
customization-editor.tsx:2097 🔥 Editor canvas available: true
customization-editor.tsx:2098 🔥 Canvas objects count: 6
customization-editor.tsx:2179 Clearing canvas selection
customization-editor.tsx:1824 Cleaning up event listeners
page.tsx:321 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
customization-editor.tsx:1818 Setting up event listeners for download and preview
customization-editor.tsx:1838 Setting up canvas object IDs for editable layers: (2) ['layer_1753675387044', 'layer_1753675393572']
customization-editor.tsx:2096 🔥 External active layer changed: null
customization-editor.tsx:2097 🔥 Editor canvas available: true
customization-editor.tsx:2098 🔥 Canvas objects count: 6
customization-editor.tsx:2179 Clearing canvas selection
customization-editor.tsx:1515 Image replaced for layer_1753675393572 at index 2 klass {filters: Array(0), cacheKey: 'texture7', crossOrigin: 'anonymous', _element: img.canvas-img, _originalElement: img.canvas-img, …}
 Cleaning up event listeners
 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: (2) ['layer_1753675387044', 'layer_1753675393572']
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
customization-editor.tsx:2179 Clearing canvas selection
page.tsx:240 🎯 handleLayerActivation called with: null
page.tsx:241 🎯 Previous activeLayerId: null
page.tsx:243 🎯 Setting activeLayerId to: null
customization-editor.tsx:1228 Canvas selection cleared
page.tsx:240 🎯 handleLayerActivation called with: null
page.tsx:241 🎯 Previous activeLayerId: null
page.tsx:243 🎯 Setting activeLayerId to: null
customization-editor.tsx:1824 Cleaning up event listeners
page.tsx:321 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
customization-editor.tsx:1818 Setting up event listeners for download and preview
customization-editor.tsx:1838 Setting up canvas object IDs for editable layers: (2) ['layer_1753675387044', 'layer_1753675393572']
customization-editor.tsx:2096 🔥 External active layer changed: null
customization-editor.tsx:2097 🔥 Editor canvas available: true
customization-editor.tsx:2098 🔥 Canvas objects count: 6
customization-editor.tsx:2179 Clearing canvas selection
customization-editor.tsx:1291 Applying customizations: {layer_1753675387044: 'Heading', layer_1753675393572: 'blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9'}
customization-editor.tsx:1292 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:1296 Processing layer layer_1753675387044 (text): Heading
customization-editor.tsx:1300 Skipping layer layer_1753675387044 - no custom value or same as original
customization-editor.tsx:1296 Processing layer layer_1753675393572 (image): blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
customization-editor.tsx:1355 Replacing image for layer_1753675393572 with: blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
customization-editor.tsx:1356 Custom value type: string, starts with {: false
customization-editor.tsx:1397 Image layer_1753675393572 already has the correct source, skipping replacement
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
customization-editor.tsx:2001 Locking non-editable objects...
customization-editor.tsx:2021 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2021 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
customization-editor.tsx:2021 🔒 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
customization-editor.tsx:2021 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:1291 Applying customizations: {layer_1753675387044: 'Heading', layer_1753675393572: 'blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9'}
customization-editor.tsx:1292 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:1296 Processing layer layer_1753675387044 (text): Heading
customization-editor.tsx:1300 Skipping layer layer_1753675387044 - no custom value or same as original
customization-editor.tsx:1296 Processing layer layer_1753675393572 (image): blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
customization-editor.tsx:1355 Replacing image for layer_1753675393572 with: blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
customization-editor.tsx:1356 Custom value type: string, starts with {: false
customization-editor.tsx:1397 Image layer_1753675393572 already has the correct source, skipping replacement
customization-editor.tsx:1291 Applying customizations: {layer_1753675387044: 'Heading', layer_1753675393572: 'blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9'}
customization-editor.tsx:1292 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:1296 Processing layer layer_1753675387044 (text): Heading
customization-editor.tsx:1300 Skipping layer layer_1753675387044 - no custom value or same as original
customization-editor.tsx:1296 Processing layer layer_1753675393572 (image): blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
customization-editor.tsx:1355 Replacing image for layer_1753675393572 with: blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
customization-editor.tsx:1356 Custom value type: string, starts with {: false
customization-editor.tsx:1397 Image layer_1753675393572 already has the correct source, skipping replacement
 Applying customizations: {layer_1753675387044: 'Heading', layer_1753675393572: 'blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9'}
 Template editable layers: (2) [{…}, {…}]
 Processing layer layer_1753675387044 (text): Heading
 Skipping layer layer_1753675387044 - no custom value or same as original
 Processing layer layer_1753675393572 (image): blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
 Replacing image for layer_1753675393572 with: blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
 Custom value type: string, starts with {: false
 Image layer_1753675393572 already has the correct source, skipping replacement
 Cleaning up event listeners
 Editor ready in public page: {savePng: ƒ, saveJpg: ƒ, saveSvg: ƒ, saveJson: ƒ, loadJson: ƒ, …}
 Setting up event listeners for download and preview
 Setting up canvas object IDs for editable layers: (2) ['layer_1753675387044', 'layer_1753675393572']
 🔥 External active layer changed: null
 🔥 Editor canvas available: true
 🔥 Canvas objects count: 6
 Clearing canvas selection
 🔒 Re-locked object: rect (ID: undefined)
 🔒 Re-locked object: image (ID: undefined)
 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
 🔒 Re-locked object: polygon (ID: undefined)
 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
 🔒 Re-locked object: textbox (ID: undefined)
 ID Assignment Verification: {expected: Array(2), assigned: Array(2), missing: Array(0)}
 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
 🔒 Locked non-editable object: polygon (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
 🔒 Locked non-editable object: textbox (ID: no-id)
 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
 Locking non-editable objects...
 🔒 Locked non-editable object: rect (ID: no-id)
 🔒 Locked non-editable object: image (ID: no-id)
 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
 🔒 Locked non-editable object: polygon (ID: no-id)
 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
customization-editor.tsx:2021 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2049 ID Assignment Verification: {expected: Array(2), assigned: Array(2), missing: Array(0)}
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
customization-editor.tsx:2001 Locking non-editable objects...
customization-editor.tsx:2021 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2021 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
customization-editor.tsx:2021 🔒 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
customization-editor.tsx:2021 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2049 ID Assignment Verification: {expected: Array(2), assigned: Array(2), missing: Array(0)}
customization-editor.tsx:1291 Applying customizations: {layer_1753675387044: 'Heading', layer_1753675393572: 'blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9'}
customization-editor.tsx:1292 Template editable layers: (2) [{…}, {…}]
customization-editor.tsx:1296 Processing layer layer_1753675387044 (text): Heading
customization-editor.tsx:1300 Skipping layer layer_1753675387044 - no custom value or same as original
customization-editor.tsx:1296 Processing layer layer_1753675393572 (image): blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
customization-editor.tsx:1355 Replacing image for layer_1753675393572 with: blob:http://localhost:3001/1e47144e-9f67-4185-a949-56b3f497adf9
customization-editor.tsx:1356 Custom value type: string, starts with {: false
customization-editor.tsx:1397 Image layer_1753675393572 already has the correct source, skipping replacement
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
customization-editor.tsx:2001 Locking non-editable objects...
customization-editor.tsx:2021 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2021 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
customization-editor.tsx:2021 🔒 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
customization-editor.tsx:2021 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2049 ID Assignment Verification: {expected: Array(2), assigned: Array(2), missing: Array(0)}
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2049 ID Assignment Verification: {expected: Array(2), assigned: Array(2), missing: Array(0)}
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675387044 for layer layer_1753675387044
customization-editor.tsx:1994 ⚠️ Object already has ID: layer_1753675393572 for layer layer_1753675393572
customization-editor.tsx:2001 Locking non-editable objects...
customization-editor.tsx:2021 🔒 Locked non-editable object: rect (ID: no-id)
customization-editor.tsx:2021 🔒 Locked non-editable object: image (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: image (ID: layer_1753675393572)
customization-editor.tsx:2021 🔒 Locked non-editable object: polygon (ID: no-id)
customization-editor.tsx:2029 🔓 Enabled interaction for editable object: textbox (ID: layer_1753675387044)
customization-editor.tsx:2021 🔒 Locked non-editable object: textbox (ID: no-id)
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)
customization-editor.tsx:2049 ID Assignment Verification: {expected: Array(2), assigned: Array(2), missing: Array(0)}
customization-editor.tsx:345 🔒 Re-locked object: rect (ID: undefined)
customization-editor.tsx:345 🔒 Re-locked object: image (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: image (ID: layer_1753675393572)
customization-editor.tsx:345 🔒 Re-locked object: polygon (ID: undefined)
customization-editor.tsx:361 🔓 Re-enabled interaction for object: textbox (ID: layer_1753675387044)
customization-editor.tsx:345 🔒 Re-locked object: textbox (ID: undefined)

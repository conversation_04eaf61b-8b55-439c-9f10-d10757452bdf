const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');

/**
 * Migration script to consolidate thumbnails to deterministic naming system
 * 
 * This script will:
 * 1. Fetch all projects from database
 * 2. For each project with a thumbnail, check if it uses old naming
 * 3. If using old naming, rename/copy to new deterministic name
 * 4. Update database to point to new filename
 * 5. Remove old thumbnail files
 * 6. Clean up orphaned thumbnail files
 */

const prisma = new PrismaClient();
const uploadsDir = path.join(process.cwd(), 'public', 'uploads');

async function migrateThumbnails() {
  console.log('🚀 Starting thumbnail migration to deterministic naming system...');
  
  try {
    // Step 1: Get all projects with thumbnails
    console.log('📊 Fetching projects from database...');
    const projects = await prisma.projects.findMany({
      where: {
        thumbnailUrl: {
          not: null,
        },
      },
      select: {
        id: true,
        thumbnailUrl: true,
        name: true,
      },
    });

    console.log(`Found ${projects.length} projects with thumbnails`);

    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Step 2: Process each project
    for (const project of projects) {
      try {
        console.log(`\n🔄 Processing project: ${project.name} (${project.id})`);
        
        const currentThumbnailUrl = project.thumbnailUrl;
        const expectedThumbnailUrl = `/uploads/thumbnail-${project.id}.jpg`;
        const expectedFilename = `thumbnail-${project.id}.jpg`;
        const expectedFilePath = path.join(uploadsDir, expectedFilename);

        // Skip if already using deterministic naming
        if (currentThumbnailUrl === expectedThumbnailUrl) {
          console.log(`✅ Already using deterministic naming: ${expectedThumbnailUrl}`);
          skippedCount++;
          continue;
        }

        // Skip data URLs (base64 thumbnails)
        if (currentThumbnailUrl.startsWith('data:')) {
          console.log(`⚠️ Skipping data URL thumbnail (will be regenerated): ${currentThumbnailUrl.substring(0, 50)}...`);
          skippedCount++;
          continue;
        }

        // Extract filename from current URL
        const currentFilename = currentThumbnailUrl.replace('/uploads/', '');
        const currentFilePath = path.join(uploadsDir, currentFilename);

        // Check if current file exists
        if (!fs.existsSync(currentFilePath)) {
          console.log(`❌ Current thumbnail file not found: ${currentFilePath}`);
          // Update database to null so it will be regenerated
          await prisma.projects.update({
            where: { id: project.id },
            data: { thumbnailUrl: null },
          });
          errorCount++;
          continue;
        }

        // Copy current file to new deterministic name
        console.log(`📁 Copying ${currentFilename} → ${expectedFilename}`);
        fs.copyFileSync(currentFilePath, expectedFilePath);

        // Update database with new URL
        await prisma.projects.update({
          where: { id: project.id },
          data: { thumbnailUrl: expectedThumbnailUrl },
        });

        console.log(`✅ Updated database: ${expectedThumbnailUrl}`);

        // Remove old file if it's different from new file
        if (currentFilePath !== expectedFilePath) {
          fs.unlinkSync(currentFilePath);
          console.log(`🗑️ Removed old file: ${currentFilename}`);
        }

        migratedCount++;

      } catch (error) {
        console.error(`❌ Error processing project ${project.id}:`, error.message);
        errorCount++;
      }
    }

    // Step 3: Clean up orphaned thumbnail files
    console.log('\n🧹 Cleaning up orphaned thumbnail files...');
    
    const allFiles = fs.readdirSync(uploadsDir);
    const thumbnailFiles = allFiles.filter(file => 
      (file.endsWith('.jpg') || file.endsWith('.png')) && 
      !file.startsWith('thumbnail-')
    );

    console.log(`Found ${thumbnailFiles.length} potential orphaned files`);

    // Get all valid project IDs for reference
    const allProjects = await prisma.projects.findMany({
      select: { id: true },
    });
    const validProjectIds = new Set(allProjects.map(p => p.id));

    let orphanedCount = 0;
    for (const file of thumbnailFiles) {
      const filePath = path.join(uploadsDir, file);
      
      // Check if this file is referenced by any project
      const isReferenced = projects.some(p => 
        p.thumbnailUrl && p.thumbnailUrl.includes(file)
      );

      if (!isReferenced) {
        try {
          fs.unlinkSync(filePath);
          console.log(`🗑️ Removed orphaned file: ${file}`);
          orphanedCount++;
        } catch (error) {
          console.error(`❌ Error removing orphaned file ${file}:`, error.message);
        }
      }
    }

    // Step 4: Summary
    console.log('\n📊 Migration Summary:');
    console.log(`✅ Successfully migrated: ${migratedCount} projects`);
    console.log(`⏭️ Skipped (already correct): ${skippedCount} projects`);
    console.log(`❌ Errors: ${errorCount} projects`);
    console.log(`🗑️ Orphaned files removed: ${orphanedCount} files`);

    // Step 5: Verify final state
    console.log('\n🔍 Verifying final state...');
    const finalFiles = fs.readdirSync(uploadsDir);
    const deterministicThumbnails = finalFiles.filter(file => 
      file.startsWith('thumbnail-') && (file.endsWith('.jpg') || file.endsWith('.png'))
    );
    
    console.log(`📁 Final thumbnail count: ${deterministicThumbnails.length} deterministic files`);
    console.log(`📁 Total files in uploads: ${finalFiles.length} files`);

    console.log('\n🎉 Thumbnail migration completed successfully!');

  } catch (error) {
    console.error('💥 Migration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateThumbnails().catch(console.error);
}

module.exports = { migrateThumbnails };

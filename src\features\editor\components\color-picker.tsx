import { ChromePicker, CirclePicker } from "react-color";

import { colors } from "@/features/editor/types";
import { rgbaObjectToString } from "@/features/editor/utils";

interface ColorPickerProps {
  value: string;
  onChange: (value: string) => void;
};

export const ColorPicker = ({
  value,
  onChange,
}: ColorPickerProps) => {
  const handleColorChange = (color: any) => {
    const formattedValue = rgbaObjectToString(color.rgb);
    onChange(formattedValue);
  };

  return (
    <div
      className="w-full space-y-4"
      onMouseDown={(e) => e.stopPropagation()}
      onMouseUp={(e) => e.stopPropagation()}
      onClick={(e) => e.stopPropagation()}
    >
      <ChromePicker
        color={value}
        onChange={handleColorChange}
        onChangeComplete={handleColorChange}
        className="border rounded-lg"
        disableAlpha={false}
      />
      <CirclePicker
        color={value}
        colors={colors}
        onChangeComplete={handleColorChange}
        circleSize={28}
        circleSpacing={14}
      />
    </div>
  );
};

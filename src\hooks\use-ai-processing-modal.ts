import { useState, useCallback } from "react";
import { AiOperationType, AiProcessingState } from "@/components/ai/ai-processing-modal";

interface UseAiProcessingModalProps {
  onCancel?: () => void;
  onRetry?: () => void;
}

export const useAiProcessingModal = ({ onCancel, onRetry }: UseAiProcessingModalProps = {}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [operationType, setOperationType] = useState<AiOperationType>("processing-image");
  const [state, setState] = useState<AiProcessingState>("idle");
  const [progress, setProgress] = useState<number | undefined>(undefined);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);
  const [customTitle, setCustomTitle] = useState<string | undefined>(undefined);
  const [customDescription, setCustomDescription] = useState<string | undefined>(undefined);

  const openModal = useCallback((
    type: AiOperationType,
    options?: {
      title?: string;
      description?: string;
      progress?: number;
    }
  ) => {
    setOperationType(type);
    setState("processing");
    setProgress(options?.progress);
    setCustomTitle(options?.title);
    setCustomDescription(options?.description);
    setErrorMessage(undefined);
    setIsOpen(true);
  }, []);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setState("idle");
    setProgress(undefined);
    setErrorMessage(undefined);
    setCustomTitle(undefined);
    setCustomDescription(undefined);
  }, []);

  const setProcessingState = useCallback((
    newState: AiProcessingState,
    options?: {
      progress?: number;
      errorMessage?: string;
    }
  ) => {
    setState(newState);
    if (options?.progress !== undefined) {
      setProgress(options.progress);
    }
    if (options?.errorMessage) {
      setErrorMessage(options.errorMessage);
    }
  }, []);

  const updateProgress = useCallback((newProgress: number) => {
    setProgress(newProgress);
  }, []);

  const setError = useCallback((message: string) => {
    setState("error");
    setErrorMessage(message);
  }, []);

  const setSuccess = useCallback(() => {
    setState("success");
    setProgress(100);
  }, []);

  const handleCancel = useCallback(() => {
    if (onCancel) {
      onCancel();
    }
    closeModal();
  }, [onCancel, closeModal]);

  const handleRetry = useCallback(() => {
    if (onRetry) {
      setState("processing");
      setErrorMessage(undefined);
      setProgress(undefined);
      onRetry();
    }
  }, [onRetry]);

  return {
    // Modal state
    isOpen,
    operationType,
    state,
    progress,
    errorMessage,
    customTitle,
    customDescription,

    // Actions
    openModal,
    closeModal,
    setProcessingState,
    updateProgress,
    setError,
    setSuccess,
    handleCancel,
    handleRetry,

    // Modal props
    modalProps: {
      isOpen,
      onClose: closeModal,
      operationType,
      state,
      progress,
      errorMessage,
      onRetry: onRetry ? handleRetry : undefined,
      onCancel: onCancel ? handleCancel : undefined,
      customTitle,
      customDescription,
    },
  };
};

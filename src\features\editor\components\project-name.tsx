"use client";

import { useState, useRef, useEffect } from "react";
import { Edit3, Check, X } from "lucide-react";

import { useUpdateProject } from "@/features/projects/api/use-update-project";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

interface ProjectNameProps {
  id: string;
  name: string;
}

export const ProjectName = ({ id, name }: ProjectNameProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(name);
  const inputRef = useRef<HTMLInputElement>(null);

  const { mutate: updateProject, isPending } = useUpdateProject();

  useEffect(() => {
    setValue(name);
  }, [name]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleSave = () => {
    const trimmedValue = value.trim();
    
    if (!trimmedValue) {
      setValue(name);
      setIsEditing(false);
      return;
    }

    if (trimmedValue !== name) {
      updateProject(
        { id, name: trimmedValue },
        {
          onSuccess: () => {
            setIsEditing(false);
          },
          onError: () => {
            setValue(name);
            setIsEditing(false);
          },
        }
      );
    } else {
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    setValue(name);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      handleCancel();
    }
  };

  if (isEditing) {
    return (
      <div className="flex items-center gap-x-2">
        <Input
          ref={inputRef}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={handleSave}
          disabled={isPending}
          className="h-8 text-sm font-medium bg-transparent border-none focus-visible:ring-1 focus-visible:ring-blue-500 px-2"
          maxLength={50}
        />
        <Button
          size="sm"
          variant="ghost"
          onClick={handleSave}
          disabled={isPending}
          className="h-6 w-6 p-0"
        >
          <Check className="size-3" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={handleCancel}
          disabled={isPending}
          className="h-6 w-6 p-0"
        >
          <X className="size-3" />
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-x-2 group">
      <span className="text-sm font-medium text-gray-900 truncate max-w-[200px]">
        {name}
      </span>
      <Button
        size="sm"
        variant="ghost"
        onClick={() => setIsEditing(true)}
        className={cn(
          "h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity",
          "hover:bg-gray-100"
        )}
      >
        <Edit3 className="size-3" />
      </Button>
    </div>
  );
};

// Fabric.js integration utilities for AI-generated content
import { fabric } from "fabric"

// Get Fabric instance (for compatibility with different versions)
export const getFabric = async () => {
  return fabric
}

// Utility to safely check if canvas is available and valid
export const isCanvasAvailable = (canvas) => {
  return canvas && typeof canvas.getActiveObject === 'function'
}

// Utility to safely get active object
export const getActiveObject = (canvas) => {
  if (!isCanvasAvailable(canvas)) {
    return null
  }

  try {
    return canvas.getActiveObject()
  } catch (error) {
    // Error getting active object - logging removed
    return null
  }
}

// Add AI-generated image to canvas
export const addImageToCanvas = async (canvas, imageUrl, options = {}) => {
  if (!isCanvasAvailable(canvas)) {
    console.error('Canvas is not available for addImageToCanvas')
    return null
  }

  if (!imageUrl) {
    console.error('Image URL is required for addImageToCanvas')
    return null
  }

  try {
    // Check if this is an AI-generated image that needs proxying
    let imageUrlToUse = imageUrl
    const needsProxy = imageUrl.includes('api.together.ai') ||
                      imageUrl.includes('fal.ai') ||
                      imageUrl.includes('replicate.delivery') ||
                      imageUrl.includes('replicate.com') ||
                      imageUrl.includes('stability.ai')

    if (needsProxy) {
      imageUrlToUse = `/api/proxy-ai-image?url=${encodeURIComponent(imageUrl)}`
      console.log('Using proxy for AI image in addImageToCanvas:', imageUrlToUse)
    }

    // Add AI-generated image to canvas
    console.log('Adding image to canvas:', {
      originalUrl: imageUrl.substring(0, 50) + '...',
      proxyUrl: needsProxy ? imageUrlToUse.substring(0, 50) + '...' : 'none'
    })

    // Use Promise-based approach with fabric.Image.fromURL
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(imageUrlToUse, (image) => {
        if (!image) {
          reject(new Error('Failed to load image'))
          return
        }

        try {
          // Set default properties
          const defaultOptions = {
            id: `ai-image-${Date.now()}`,
            padding: 10,
            cornerSize: 10,
            imageSmoothing: true,
            strokeWidth: 0,
            stroke: null,
            selectable: true,
            evented: true,
            ...options
          }

          image.set(defaultOptions)

          // Smart scaling: fit to canvas boundaries while maintaining aspect ratio
          const canvasWidth = canvas.getWidth()
          const canvasHeight = canvas.getHeight()
          const maxDimension = options.maxDimension || Math.min(canvasWidth * 0.8, canvasHeight * 0.8, 800)

          // Calculate scale to fit within boundaries while showing the whole image
          if (image.width > maxDimension || image.height > maxDimension) {
            const scale = Math.min(maxDimension / image.width, maxDimension / image.height)
            image.scale(scale)
            console.log('Scaled down large image:', {
              originalSize: `${image.width}x${image.height}`,
              scale,
              newSize: `${image.width * scale}x${image.height * scale}`
            })
          } else if (image.width < 100 && image.height < 100) {
            // Scale up very small images to be more visible
            const minDimension = 150
            const scale = Math.max(minDimension / image.width, minDimension / image.height)
            image.scale(scale)
            console.log('Scaled up small image:', {
              originalSize: `${image.width}x${image.height}`,
              scale,
              newSize: `${image.width * scale}x${image.height * scale}`
            })
          }

          // Position image (center by default or use provided position)
          if (!options.left && !options.top) {
            const canvasCenter = canvas.getCenter()
            image.set({
              left: canvasCenter.left,
              top: canvasCenter.top,
              originX: 'center',
              originY: 'center'
            })
          }

          // Add to canvas
          canvas.add(image)
          canvas.setActiveObject(image)
          canvas.renderAll()

          // Successfully added image to canvas
          resolve(image)
        } catch (error) {
          console.error('Error setting up image:', error)
          reject(error)
        }
      }, {
        crossOrigin: 'anonymous'
      })
    })
  } catch (error) {
    console.error("Error adding AI image to canvas:", error)
    throw new Error(`Failed to add image to canvas: ${error.message}`)
  }
}

// Replace selected object with AI-generated image
export const replaceSelectedWithImage = async (canvas, imageUrl, options = {}) => {
  if (!canvas) {
    console.error('Canvas is required for replaceSelectedWithImage')
    return null
  }

  if (!imageUrl) {
    console.error('Image URL is required for replaceSelectedWithImage')
    return null
  }

  const activeObject = canvas.getActiveObject()
  if (!activeObject) {
    console.warn('No object selected')
    return null
  }

  try {
    // Get the exact position in the layer stack BEFORE removing the object
    const objectIndex = canvas.getObjects().indexOf(activeObject)
    console.log(`Replacing object at layer index ${objectIndex}`)

    // Get position and size of selected object
    const objectBounds = {
      left: activeObject.left,
      top: activeObject.top,
      width: activeObject.width * activeObject.scaleX,
      height: activeObject.height * activeObject.scaleY,
      angle: activeObject.angle
    }

    // Remove selected object
    canvas.remove(activeObject)

    // Add new image at the same position
    const image = await addImageToCanvas(canvas, imageUrl, {
      ...options,
      left: objectBounds.left,
      top: objectBounds.top,
      maxDimension: Math.max(objectBounds.width, objectBounds.height)
    })

    // Apply similar transformations
    if (objectBounds.angle) {
      image.set('angle', objectBounds.angle)
    }

    // Preserve layer order by moving the new image to the correct position
    if (objectIndex !== -1 && image) {
      // Remove from current position and insert at original position
      canvas.remove(image)
      canvas.insertAt(image, objectIndex, false)
      canvas.setActiveObject(image)
      console.log(`Successfully repositioned image to layer index ${objectIndex}`)
    }

    canvas.renderAll()
    return image
  } catch (error) {
    console.error("Error replacing object with AI image:", error)
    throw error
  }
}

// Apply AI-processed image to selected object
export const applyProcessedImageToSelected = async (canvas, processedImageUrl) => {
  if (!canvas) {
    console.error('Canvas is required for applyProcessedImageToSelected')
    return null
  }

  if (!processedImageUrl) {
    console.error('Processed image URL is required')
    return null
  }

  const activeObject = canvas.getActiveObject()
  if (!activeObject || activeObject.type !== 'image') {
    console.warn('Please select an image object')
    return null
  }

  try {
    console.log('Applying processed image to selected object', {
      imageUrl: processedImageUrl.substring(0, 50) + '...',
      selectedObjectType: activeObject.type,
      selectedObjectId: activeObject.id,
      urlType: typeof processedImageUrl,
      urlLength: processedImageUrl.length,
      isValidUrl: processedImageUrl.startsWith('http') || processedImageUrl.startsWith('data:')
    })

    // Validate URL format
    if (!processedImageUrl.startsWith('http') && !processedImageUrl.startsWith('data:')) {
      console.error('Invalid image URL format:', processedImageUrl)
      throw new Error('Invalid image URL format')
    }

    // Check if this is an AI-generated image that needs proxying
    let imageUrlToUse = processedImageUrl
    const needsProxy = processedImageUrl.includes('api.together.ai') ||
                      processedImageUrl.includes('fal.ai') ||
                      processedImageUrl.includes('replicate.delivery') ||
                      processedImageUrl.includes('replicate.com') ||
                      processedImageUrl.includes('stability.ai')

    if (needsProxy) {
      imageUrlToUse = `/api/proxy-ai-image?url=${encodeURIComponent(processedImageUrl)}`
      console.log('Using proxy for AI image:', imageUrlToUse)
    }

    // Use Promise-based approach with fabric.Image.fromURL
    return new Promise((resolve, reject) => {
      console.log('Starting fabric.Image.fromURL with URL:', imageUrlToUse.substring(0, 100) + '...')

      fabric.Image.fromURL(imageUrlToUse, (newImage, isError) => {
        console.log('fabric.Image.fromURL callback called', {
          hasNewImage: !!newImage,
          isError: !!isError,
          errorDetails: isError
        })

        if (isError || !newImage) {
          console.error('Failed to load processed image from URL:', isError)
          reject(new Error('Failed to load processed image'))
          return
        }

        console.log('Processed image loaded successfully', {
          newImageWidth: newImage.width,
          newImageHeight: newImage.height,
          newImageSrc: newImage.getSrc()
        })

        try {
          // Preserve ALL original object properties for perfect positioning and behavior
          const originalProps = {
            // Position and transformation
            left: activeObject.left,
            top: activeObject.top,
            angle: activeObject.angle || 0,
            scaleX: activeObject.scaleX || 1,
            scaleY: activeObject.scaleY || 1,
            flipX: activeObject.flipX || false,
            flipY: activeObject.flipY || false,
            skewX: activeObject.skewX || 0,
            skewY: activeObject.skewY || 0,

            // Visual properties
            opacity: activeObject.opacity || 1,
            visible: activeObject.visible !== false,

            // Interaction properties
            selectable: activeObject.selectable !== false,
            evented: activeObject.evented !== false,
            hasControls: activeObject.hasControls !== false,
            hasBorders: activeObject.hasBorders !== false,
            lockMovementX: activeObject.lockMovementX || false,
            lockMovementY: activeObject.lockMovementY || false,
            lockRotation: activeObject.lockRotation || false,
            lockScalingX: activeObject.lockScalingX || false,
            lockScalingY: activeObject.lockScalingY || false,
            lockSkewingX: activeObject.lockSkewingX || false,
            lockSkewingY: activeObject.lockSkewingY || false,

            // Control properties
            padding: activeObject.padding || 10,
            cornerSize: activeObject.cornerSize || 10,
            cornerStyle: activeObject.cornerStyle || 'rect',
            cornerColor: activeObject.cornerColor || 'rgba(102,153,255,0.75)',
            cornerStrokeColor: activeObject.cornerStrokeColor || '',
            borderColor: activeObject.borderColor || 'rgba(102,153,255,0.75)',
            borderDashArray: activeObject.borderDashArray || null,
            borderOpacityWhenMoving: activeObject.borderOpacityWhenMoving || 0.4,
            borderScaleFactor: activeObject.borderScaleFactor || 1,

            // Identification
            id: activeObject.id || `processed-image-${Date.now()}`,
            name: activeObject.name || '',

            // Custom properties that might exist
            ...(activeObject.customProperties || {})
          }

          // Calculate the current displayed dimensions to maintain exact size
          const currentDisplayWidth = activeObject.width * activeObject.scaleX
          const currentDisplayHeight = activeObject.height * activeObject.scaleY

          console.log('Preserving original object properties', {
            originalWidth: activeObject.width,
            originalHeight: activeObject.height,
            originalScaleX: activeObject.scaleX,
            originalScaleY: activeObject.scaleY,
            currentDisplayWidth,
            currentDisplayHeight,
            newImageWidth: newImage.width,
            newImageHeight: newImage.height
          })

          // Simple and reliable scaling: fit the new image exactly within the original image's display area
          const scaleToFitX = currentDisplayWidth / newImage.width
          const scaleToFitY = currentDisplayHeight / newImage.height

          // Use the smaller scale factor to ensure the entire image fits (fit mode)
          // This maintains aspect ratio and prevents any cropping
          const uniformScale = Math.min(scaleToFitX, scaleToFitY)

          // Ensure scale is reasonable (prevent extreme values)
          const finalScale = Math.max(0.001, Math.min(1000, uniformScale))

          console.log('Simple uniform scaling for processed image replacement', {
            originalDisplaySize: { width: currentDisplayWidth, height: currentDisplayHeight },
            newImageSize: { width: newImage.width, height: newImage.height },
            scaleToFitX: scaleToFitX.toFixed(3),
            scaleToFitY: scaleToFitY.toFixed(3),
            uniformScale: uniformScale.toFixed(3),
            finalScale: finalScale.toFixed(3),
            resultSize: {
              width: (newImage.width * finalScale).toFixed(1),
              height: (newImage.height * finalScale).toFixed(1)
            },
            scalingBehavior: finalScale > 1 ? 'SCALING UP (small image)' : 'SCALING DOWN (large image)',
            aspectRatioPreserved: true,
            wholeImageVisible: true
          })

          // Apply ALL original properties to the new image with uniform scaling
          newImage.set({
            ...originalProps,
            scaleX: finalScale,
            scaleY: finalScale
          })

          // CRITICAL: Get the exact position in the layer stack BEFORE removing the object
          const allObjects = canvas.getObjects()
          const objectIndex = allObjects.indexOf(activeObject)
          const totalObjects = allObjects.length

          console.log(`Preserving layer order: object at index ${objectIndex} of ${totalObjects} total objects`)

          // Store references to objects before and after for verification
          const objectBefore = objectIndex > 0 ? allObjects[objectIndex - 1] : null
          const objectAfter = objectIndex < totalObjects - 1 ? allObjects[objectIndex + 1] : null

          // Remove the original object from canvas
          canvas.remove(activeObject)

          // Insert the new image at the exact same layer position
          if (objectIndex !== -1 && objectIndex < totalObjects) {
            canvas.insertAt(newImage, objectIndex, false)
            console.log(`Successfully inserted new image at layer index ${objectIndex}`)

            // Verify layer order is preserved
            const newAllObjects = canvas.getObjects()
            const newObjectIndex = newAllObjects.indexOf(newImage)
            const newObjectBefore = newObjectIndex > 0 ? newAllObjects[newObjectIndex - 1] : null
            const newObjectAfter = newObjectIndex < newAllObjects.length - 1 ? newAllObjects[newObjectIndex + 1] : null

            console.log('Layer order verification', {
              originalIndex: objectIndex,
              newIndex: newObjectIndex,
              beforeMatches: objectBefore === newObjectBefore,
              afterMatches: objectAfter === newObjectAfter
            })
          } else {
            // Fallback to regular add if index not found (should rarely happen)
            console.warn('Could not determine object index, using fallback add')
            canvas.add(newImage)
          }

          // Ensure the new image coordinates are set correctly
          newImage.setCoords()

          // Set as active object and render
          canvas.setActiveObject(newImage)
          canvas.renderAll()

          // Final verification of the applied properties
          const finalVerification = {
            finalDisplayWidth: newImage.width * newImage.scaleX,
            finalDisplayHeight: newImage.height * newImage.scaleY,
            finalPosition: { left: newImage.left, top: newImage.top },
            finalScale: { scaleX: newImage.scaleX, scaleY: newImage.scaleY },
            finalAngle: newImage.angle,
            finalOpacity: newImage.opacity,
            hasControls: newImage.hasControls,
            hasBorders: newImage.hasBorders,
            selectable: newImage.selectable,
            layerIndex: canvas.getObjects().indexOf(newImage)
          }

          console.log('AI-edited image successfully applied with preserved properties:', finalVerification)
          resolve(newImage)
        } catch (error) {
          console.error('Error setting up processed image:', error)
          reject(error)
        }
      }, {
        crossOrigin: 'anonymous'
      })

      // Add timeout to prevent hanging
      setTimeout(() => {
        console.warn('Image loading timeout after 30 seconds')
        reject(new Error('Image loading timeout'))
      }, 30000)
    })
  } catch (error) {
    console.error("Error applying processed image:", error)
    throw new Error(`Failed to apply processed image: ${error.message}`)
  }
}

// Create image from data URL (for client-side processed images)
export const addDataUrlImageToCanvas = async (canvas, dataUrl, options = {}) => {
  return addImageToCanvas(canvas, dataUrl, options)
}

// Get image data URL from selected object
export const getSelectedImageDataUrl = (canvas, format = 'png', quality = 1.0) => {
  if (!canvas) {
    console.warn('Canvas not provided to getSelectedImageDataUrl')
    return null
  }

  try {
    const activeObject = canvas.getActiveObject()
    if (!activeObject || activeObject.type !== 'image') {
      console.warn('No image object selected')
      return null
    }

    // Create temporary canvas for the selected object
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')

    // Set canvas size to object size
    const objectBounds = activeObject.getBoundingRect()
    tempCanvas.width = objectBounds.width
    tempCanvas.height = objectBounds.height

    // Get the image element
    const imageElement = activeObject._originalElement || activeObject._element
    if (!imageElement) {
      console.warn('Could not access image element')
      return null
    }

    // Draw the image to temporary canvas
    tempCtx.drawImage(
      imageElement,
      0, 0,
      imageElement.width, imageElement.height,
      0, 0,
      tempCanvas.width, tempCanvas.height
    )

    // Return data URL
    return tempCanvas.toDataURL(`image/${format}`, quality)
  } catch (error) {
    console.error("Error getting image data URL:", error)
    return null
  }
}

// Get selected image URL for AI processing (converts localhost URLs to data URLs)
export const getSelectedImageUrlForAI = (canvas) => {
  if (!isCanvasAvailable(canvas)) {
    console.warn('Canvas not available for getSelectedImageUrlForAI')
    return null
  }

  try {
    const activeObject = getActiveObject(canvas)
    if (!activeObject || activeObject.type !== 'image') {
      return null
    }

    // Try to get the original source URL first
    const imageElement = activeObject._originalElement || activeObject._element
    const originalUrl = imageElement?.src || imageElement?.currentSrc

    // If we have a valid HTTP URL, check if it's a localhost upload
    if (originalUrl && originalUrl.startsWith('http')) {
      // If it's a localhost upload, convert to data URL for AI processing
      if (originalUrl.includes('localhost') || originalUrl.includes('127.0.0.1') || originalUrl.includes('/uploads/')) {
        console.log('Converting localhost image to data URL for AI processing:', originalUrl)
        return getImageDataUrl(activeObject)
      }
      // Otherwise return the HTTP URL as-is
      return originalUrl
    }

    // If we have a data URL, return it
    if (originalUrl && originalUrl.startsWith('data:')) {
      return originalUrl
    }

    // If no valid URL, convert the image object to data URL
    return getImageDataUrl(activeObject)
  } catch (error) {
    console.error("Error getting image URL for AI:", error)
    return null
  }
}

// Get selected image URL (if available) - for general use
export const getSelectedImageUrl = (canvas) => {
  if (!isCanvasAvailable(canvas)) {
    console.warn('Canvas not available for getSelectedImageUrl')
    return null
  }

  try {
    const activeObject = getActiveObject(canvas)
    if (!activeObject || activeObject.type !== 'image') {
      return null
    }

    // Try to get the original source URL first
    const imageElement = activeObject._originalElement || activeObject._element
    const originalUrl = imageElement?.src || imageElement?.currentSrc

    // If we have a valid HTTP URL, return it
    if (originalUrl && (originalUrl.startsWith('http') || originalUrl.startsWith('data:'))) {
      return originalUrl
    }

    // If no valid URL, convert the image object to data URL
    return getImageDataUrl(activeObject)
  } catch (error) {
    console.error("Error getting image URL:", error)
    return null
  }
}

// Convert fabric image object to data URL
export const getImageDataUrl = (imageObject) => {
  try {
    if (!imageObject || imageObject.type !== 'image') {
      return null
    }

    // Get the image element
    const imageElement = imageObject._originalElement || imageObject._element
    if (!imageElement) {
      console.warn('No image element found in fabric object')
      return null
    }

    // Check if image is loaded
    if (imageElement.complete === false) {
      console.warn('Image not fully loaded, cannot convert to data URL')
      return null
    }

    // Create a temporary canvas to extract the image data
    const tempCanvas = document.createElement('canvas')
    const ctx = tempCanvas.getContext('2d')

    // Set canvas size to match the original image dimensions
    const width = imageObject.width || imageElement.naturalWidth || imageElement.width
    const height = imageObject.height || imageElement.naturalHeight || imageElement.height

    tempCanvas.width = width
    tempCanvas.height = height

    // Draw the image to the temporary canvas
    ctx.drawImage(imageElement, 0, 0, width, height)

    // Convert to data URL with good quality
    return tempCanvas.toDataURL('image/png', 0.9)
  } catch (error) {
    console.error("Error converting image to data URL:", error)
    return null
  }
}

// Batch add multiple AI images
export const addMultipleImagesToCanvas = async (canvas, imageUrls, options = {}) => {
  if (!canvas || !Array.isArray(imageUrls)) {
    throw new Error('Canvas and image URLs array are required')
  }

  const results = []
  const spacing = options.spacing || 50
  const startX = options.startX || 100
  const startY = options.startY || 100

  for (let i = 0; i < imageUrls.length; i++) {
    try {
      const imageOptions = {
        ...options,
        left: startX + (i * spacing),
        top: startY + (Math.floor(i / 5) * spacing), // Arrange in rows of 5
        maxDimension: options.maxDimension || 200
      }

      const image = await addImageToCanvas(canvas, imageUrls[i], imageOptions)
      results.push(image)
    } catch (error) {
      console.error(`Failed to add image ${i}:`, error)
      results.push(null)
    }
  }

  return results
}

// Utility to check if object is an AI-generated image
export const isAiGeneratedImage = (object) => {
  return object && 
         object.type === 'image' && 
         (object.id?.includes('ai-image') || object.id?.includes('processed-image'))
}

// Get all AI images on canvas
export const getAiImagesOnCanvas = (canvas) => {
  if (!canvas) return []
  
  return canvas.getObjects().filter(isAiGeneratedImage)
}

// Remove all AI images from canvas
export const removeAllAiImages = (canvas) => {
  if (!canvas) return
  
  const aiImages = getAiImagesOnCanvas(canvas)
  aiImages.forEach(image => canvas.remove(image))
  canvas.renderAll()
  
  return aiImages.length
}

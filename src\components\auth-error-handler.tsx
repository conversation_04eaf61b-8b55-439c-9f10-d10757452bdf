'use client';

import { useEffect } from 'react';

export function AuthErrorHandler() {
  useEffect(() => {
    // Function to clear NextAuth cookies
    const clearAuthCookies = () => {
      const authCookieNames = [
        'next-auth.session-token',
        '__Secure-next-auth.session-token',
        'next-auth.csrf-token',
        '__Host-next-auth.csrf-token',
        'next-auth.callback-url',
        '__Secure-next-auth.callback-url'
      ];
      
      authCookieNames.forEach(cookieName => {
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=localhost;`;
      });
    };

    // Add global function for manual clearing
    (window as any).clearAuthCookies = () => {
      clearAuthCookies();
      console.log('Auth cookies cleared. Please refresh the page.');
      window.location.reload();
    };

    // Check for JWT errors in console and auto-clear if needed
    const originalConsoleError = console.error;
    console.error = function(...args) {
      const errorMessage = args.join(' ');
      
      if (errorMessage.includes('JWTSessionError') || 
          errorMessage.includes('no matching decryption secret')) {
        console.warn('JWT session error detected. You can clear cookies by running clearAuthCookies() in the console.');
      }
      
      originalConsoleError.apply(console, args);
    };

    return () => {
      console.error = originalConsoleError;
    };
  }, []);

  return null;
}

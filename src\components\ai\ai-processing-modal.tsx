"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>ircle2, X, AlertCircle, Loader2, Spark<PERSON> } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";

export type AiOperationType = 
  | "generating-image"
  | "removing-background" 
  | "applying-filter"
  | "upscaling-image"
  | "generating-variations"
  | "editing-image"
  | "processing-image";

export type AiProcessingState = 
  | "idle"
  | "processing" 
  | "success"
  | "error";

interface AiProcessingModalProps {
  isOpen: boolean;
  onClose: () => void;
  operationType: AiOperationType;
  state: AiProcessingState;
  progress?: number;
  errorMessage?: string;
  onRetry?: () => void;
  onCancel?: () => void;
  customTitle?: string;
  customDescription?: string;
}

const getOperationConfig = (type: AiOperationType) => {
  const configs = {
    "generating-image": {
      title: "Generating Image",
      description: "Creating your image with AI...",
      processingText: "This may take a few moments",
      successText: "Image generated successfully!",
      icon: Sparkles,
    },
    "removing-background": {
      title: "Removing Background",
      description: "Processing your image...",
      processingText: "Analyzing and removing background",
      successText: "Background removed successfully!",
      icon: Sparkles,
    },
    "applying-filter": {
      title: "Applying Filter",
      description: "Enhancing your image...",
      processingText: "Applying AI-powered effects",
      successText: "Filter applied successfully!",
      icon: Sparkles,
    },
    "upscaling-image": {
      title: "Upscaling Image",
      description: "Enhancing image resolution...",
      processingText: "Increasing image quality and size",
      successText: "Image upscaled successfully!",
      icon: Sparkles,
    },
    "generating-variations": {
      title: "Generating Variations",
      description: "Creating image variations...",
      processingText: "Generating alternative versions",
      successText: "Variations generated successfully!",
      icon: Sparkles,
    },
    "editing-image": {
      title: "Editing Image",
      description: "Applying AI modifications...",
      processingText: "Processing your edit request",
      successText: "Image edited successfully!",
      icon: Sparkles,
    },
    "processing-image": {
      title: "Processing Image",
      description: "Working on your image...",
      processingText: "AI is processing your request",
      successText: "Processing completed successfully!",
      icon: Sparkles,
    },
  };

  return configs[type];
};

export const AiProcessingModal = ({
  isOpen,
  onClose,
  operationType,
  state,
  progress,
  errorMessage,
  onRetry,
  onCancel,
  customTitle,
  customDescription,
}: AiProcessingModalProps) => {
  const [showSuccess, setShowSuccess] = useState(false);
  const config = getOperationConfig(operationType);
  const IconComponent = config.icon;

  // Auto-close success state after 2 seconds
  useEffect(() => {
    if (state === "success") {
      setShowSuccess(true);
      const timer = setTimeout(() => {
        setShowSuccess(false);
        onClose();
      }, 2000);

      return () => clearTimeout(timer);
    } else {
      setShowSuccess(false);
    }
  }, [state, onClose]);

  // Don't render if not open
  if (!isOpen) return null;

  const handleClose = () => {
    if (state === "processing" && onCancel) {
      onCancel();
    } else if (state !== "processing") {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center space-y-4">
          <div className="mx-auto flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-blue-500">
            {state === "processing" && (
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            )}
            {state === "success" && (
              <CheckCircle2 className="w-8 h-8 text-white" />
            )}
            {state === "error" && (
              <AlertCircle className="w-8 h-8 text-white" />
            )}
            {state === "idle" && (
              <IconComponent className="w-8 h-8 text-white" />
            )}
          </div>

          <div className="space-y-2">
            <DialogTitle className="text-xl font-semibold">
              {state === "success" 
                ? config.successText
                : state === "error"
                ? "Processing Failed"
                : customTitle || config.title
              }
            </DialogTitle>
            
            <DialogDescription className="text-sm text-muted-foreground">
              {state === "error" 
                ? errorMessage || "An error occurred while processing your request"
                : state === "success"
                ? "Your AI operation completed successfully"
                : customDescription || config.description
              }
            </DialogDescription>
          </div>

          {/* Progress indicator */}
          {state === "processing" && (
            <div className="space-y-3">
              {typeof progress === "number" ? (
                <div className="space-y-2">
                  <Progress value={progress} className="w-full" />
                  <p className="text-xs text-muted-foreground">
                    {progress}% complete
                  </p>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                  </div>
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                {config.processingText}
              </p>
            </div>
          )}

          {/* Success animation */}
          {state === "success" && showSuccess && (
            <div className="flex items-center justify-center">
              <div className="w-4 h-4 bg-green-500 rounded-full animate-ping"></div>
            </div>
          )}
        </DialogHeader>

        {/* Action buttons */}
        <div className="flex justify-center space-x-2 pt-4">
          {state === "error" && (
            <>
              {onRetry && (
                <Button onClick={onRetry} className="flex-1">
                  Try Again
                </Button>
              )}
              <Button variant="outline" onClick={onClose} className="flex-1">
                Close
              </Button>
            </>
          )}
          
          {state === "processing" && onCancel && (
            <Button variant="outline" onClick={onCancel}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
          )}

          {state === "idle" && (
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

const fs = require('fs');
const path = require('path');

// Enhanced script to clean up excessive thumbnail files and prepare for migration
const uploadsDir = path.join(process.cwd(), 'public', 'uploads');

console.log('🧹 Cleaning up excessive thumbnail files and preparing for deterministic naming...');

try {
  const files = fs.readdirSync(uploadsDir);
  console.log(`Found ${files.length} files in uploads directory`);

  // Separate deterministic thumbnails from random-named files
  const deterministicThumbnails = files.filter(file =>
    file.startsWith('thumbnail-') && (file.endsWith('.jpg') || file.endsWith('.png'))
  );

  const randomThumbnails = files.filter(file =>
    (file.endsWith('.jpg') || file.endsWith('.png')) &&
    !file.startsWith('thumbnail-') &&
    /^\d+-.+\.(jpg|png)$/.test(file) // Matches timestamp-random pattern
  );

  const otherFiles = files.filter(file =>
    !deterministicThumbnails.includes(file) &&
    !randomThumbnails.includes(file)
  );

  console.log(`📊 File breakdown:`);
  console.log(`  - Deterministic thumbnails: ${deterministicThumbnails.length}`);
  console.log(`  - Random thumbnails: ${randomThumbnails.length}`);
  console.log(`  - Other files: ${otherFiles.length}`);

  // Keep only the most recent 10 random thumbnails (for migration purposes)
  const sortedRandomFiles = randomThumbnails
    .map(file => ({
      name: file,
      timestamp: parseInt(file.split('-')[0]) || 0,
      path: path.join(uploadsDir, file),
      stats: fs.statSync(path.join(uploadsDir, file))
    }))
    .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime()); // Sort by modification time

  const randomFilesToKeep = sortedRandomFiles.slice(0, 10);
  const randomFilesToDelete = sortedRandomFiles.slice(10);

  console.log(`\n🗑️ Cleaning up random thumbnails:`);
  console.log(`  - Keeping ${randomFilesToKeep.length} most recent random thumbnails`);
  console.log(`  - Deleting ${randomFilesToDelete.length} old random thumbnails`);

  let deletedCount = 0;
  randomFilesToDelete.forEach(file => {
    try {
      fs.unlinkSync(file.path);
      deletedCount++;
    } catch (error) {
      console.error(`Failed to delete ${file.name}:`, error.message);
    }
  });

  console.log(`✅ Successfully deleted ${deletedCount} old random thumbnail files`);

  // Summary
  const remainingFiles = fs.readdirSync(uploadsDir);
  console.log(`\n📊 Final state:`);
  console.log(`  - Total files remaining: ${remainingFiles.length}`);
  console.log(`  - Deterministic thumbnails: ${deterministicThumbnails.length}`);
  console.log(`  - Random thumbnails kept: ${randomFilesToKeep.length}`);
  console.log(`  - Ready for migration: ${randomFilesToKeep.length > 0 ? 'Yes' : 'No'}`);

  if (randomFilesToKeep.length > 0) {
    console.log(`\n💡 Next step: Run 'node migrate-thumbnails.js' to convert remaining random thumbnails to deterministic naming`);
  }

} catch (error) {
  console.error('❌ Error cleaning up thumbnails:', error.message);
}

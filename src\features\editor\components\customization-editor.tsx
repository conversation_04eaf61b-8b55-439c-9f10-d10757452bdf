"use client";

import { useEffect, useRef, useCallback, useState, useMemo } from "react";
import { fabric } from "fabric";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Footer } from "@/features/editor/components/footer";
import { Toolbar } from "@/features/editor/components/toolbar";

import { EditableLayer } from "@/types/template";
import { ActiveTool } from "@/features/editor/types";
import debounce from "lodash.debounce";
import { Loader2 } from "lucide-react";

interface CustomizationEditorProps {
  templateData: {
    id: string;
    name: string;
    width: number;
    height: number;
    json: string;
    editableLayers: EditableLayer[];
  };
  customizations: Record<string, string>;
  onCustomizationChange: (layerId: string, value: string) => void;
  onPreviewGenerated: (dataUrl: string) => void;
  activeLayerId?: string | null;
  onLayerActivation?: (layerId: string | null) => void;
  onEditorReady?: (editor: any) => void;
  isPublicCustomization?: boolean; // Flag to indicate this is public customization (read-only template)
}

export const CustomizationEditor = ({
  templateData,
  customizations,
  onCustomizationChange,
  onPreviewGenerated,
  activeLayerId: externalActiveLayerId,
  onLayerActivation,
  onEditorReady,
  isPublicCustomization = false,
}: CustomizationEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isCanvasLoading, setIsCanvasLoading] = useState(true);
  const [activeTool, setActiveTool] = useState<ActiveTool>("select");
  const isApplyingCustomizations = useRef(false);
  const [isRemovingBackground, setIsRemovingBackground] = useState(false);
  const [isAiEditing, setIsAiEditing] = useState(false);


  // Helper function to calculate optimal image scaling with workspace boundary awareness
  const calculateImageScale = (
    originalDisplayWidth: number,
    originalDisplayHeight: number,
    newImageWidth: number,
    newImageHeight: number,
    fillMode: 'fit' | 'fill' = 'fit',
    originalLeft?: number,
    originalTop?: number,
    workspaceBounds?: { left: number; top: number; width: number; height: number }
  ) => {
    const scaleToFitX = originalDisplayWidth / newImageWidth;
    const scaleToFitY = originalDisplayHeight / newImageHeight;

    // Choose scaling strategy based on mode
    let uniformScale: number;

    if (fillMode === 'fill') {
      // Fill mode: Use Math.max to fill the boundary completely
      // This may crop the image but ensures the boundary is filled
      uniformScale = Math.max(scaleToFitX, scaleToFitY);
    } else {
      // Fit mode: Use Math.min to ensure the entire image is visible
      // This ensures no cropping but may leave empty space
      uniformScale = Math.min(scaleToFitX, scaleToFitY);
    }

    const scaledWidth = newImageWidth * uniformScale;
    const scaledHeight = newImageHeight * uniformScale;

    // If we have workspace bounds and original position, check if scaled image would exceed workspace
    let finalScale = uniformScale;
    if (workspaceBounds && originalLeft !== undefined && originalTop !== undefined) {
      const imageRight = originalLeft + scaledWidth;
      const imageBottom = originalTop + scaledHeight;
      const workspaceRight = workspaceBounds.left + workspaceBounds.width;
      const workspaceBottom = workspaceBounds.top + workspaceBounds.height;

      // If image would exceed workspace boundaries, scale it down to fit
      if (imageRight > workspaceRight || imageBottom > workspaceBottom) {
        const maxAllowedWidth = workspaceRight - originalLeft;
        const maxAllowedHeight = workspaceBottom - originalTop;
        const constrainedScaleX = maxAllowedWidth / newImageWidth;
        const constrainedScaleY = maxAllowedHeight / newImageHeight;
        finalScale = Math.min(uniformScale, constrainedScaleX, constrainedScaleY);

        console.log('🚨 Image would exceed workspace, constraining scale:', {
          originalScale: uniformScale,
          constrainedScale: finalScale,
          reason: 'workspace boundary constraint'
        });
      }
    }

    const finalScaledWidth = newImageWidth * finalScale;
    const finalScaledHeight = newImageHeight * finalScale;

    console.log('🔍 Image scaling logic:', {
      mode: fillMode,
      boundary: { width: originalDisplayWidth, height: originalDisplayHeight },
      newImage: { width: newImageWidth, height: newImageHeight },
      scales: { x: scaleToFitX, y: scaleToFitY, uniform: uniformScale, final: finalScale },
      result: { width: finalScaledWidth, height: finalScaledHeight },
      behavior: finalScale > 1 ? 'SCALING UP' : 'SCALING DOWN',
      workspaceConstrained: finalScale !== uniformScale
    });

    return {
      uniformScale: finalScale,
      scaleToFitX,
      scaleToFitY,
      scaledWidth: finalScaledWidth,
      scaledHeight: finalScaledHeight
    };
  };

  // Helper function to create a unique identifier for image objects
  const createImageUniqueId = (obj: any) => {
    const left = Math.round(obj.left || 0);
    const top = Math.round(obj.top || 0);
    const width = Math.round((obj.width || 0) * (obj.scaleX || 1));
    const height = Math.round((obj.height || 0) * (obj.scaleY || 1));
    return `${left}_${top}_${width}_${height}`;
  };

  // Helper function to preserve all visual properties from original image
  const preserveImageProperties = (originalObject: any) => {
    return {
      // Position and dimensions
      left: originalObject.left || 0,
      top: originalObject.top || 0,
      width: originalObject.width || 100,
      height: originalObject.height || 100,

      // Scaling and transformation
      scaleX: originalObject.scaleX || 1,
      scaleY: originalObject.scaleY || 1,
      angle: originalObject.angle || 0,
      skewX: originalObject.skewX || 0,
      skewY: originalObject.skewY || 0,

      // Origin points
      originX: originalObject.originX || 'left',
      originY: originalObject.originY || 'top',

      // Flipping
      flipX: originalObject.flipX || false,
      flipY: originalObject.flipY || false,

      // Visual properties
      opacity: originalObject.opacity !== undefined ? originalObject.opacity : 1,
      visible: originalObject.visible !== false,
      fill: originalObject.fill,

      // Effects and filters
      shadow: originalObject.shadow || null,
      filters: originalObject.filters ? [...originalObject.filters] : [],

      // Stroke properties
      stroke: originalObject.stroke || undefined,
      strokeWidth: originalObject.strokeWidth || 0,
      strokeDashArray: originalObject.strokeDashArray || null,

      // Interaction properties
      selectable: originalObject.selectable !== false,
      evented: originalObject.evented !== false,
      hasControls: originalObject.hasControls !== false,
      hasBorders: originalObject.hasBorders !== false,

      // Corner and border styling
      cornerStyle: originalObject.cornerStyle || 'rect',
      cornerSize: originalObject.cornerSize || 13,
      cornerColor: originalObject.cornerColor || 'rgba(178,204,255,0.8)',
      borderColor: originalObject.borderColor || 'rgba(102,153,255,0.75)',
      borderScaleFactor: originalObject.borderScaleFactor || 1,

      // Lock properties
      lockMovementX: originalObject.lockMovementX || false,
      lockMovementY: originalObject.lockMovementY || false,
      lockRotation: originalObject.lockRotation || false,
      lockScalingX: originalObject.lockScalingX || false,
      lockScalingY: originalObject.lockScalingY || false,
      lockUniScaling: originalObject.lockUniScaling || false,
      lockSkewingX: originalObject.lockSkewingX || false,
      lockSkewingY: originalObject.lockSkewingY || false,

      // Custom properties
      id: (originalObject as any).id,
    };
  };



  // Helper function to apply preserved properties to new image with visual continuity
  const applyPreservedProperties = (
    newImage: fabric.Image,
    originalProps: ReturnType<typeof preserveImageProperties>,
    adjustedLeft?: number,
    adjustedTop?: number,
    uniformScale?: number
  ) => {
    // Calculate the target visual dimensions
    const targetVisualHeight = (originalProps.height || 100) * (originalProps.scaleY || 1);

    // If we have a uniform scale, use it to set the image scaling correctly
    if (uniformScale !== undefined) {
      // Apply all preserved properties with the new scale
      newImage.set({
        ...originalProps,
        left: adjustedLeft !== undefined ? adjustedLeft : originalProps.left,
        top: adjustedTop !== undefined ? adjustedTop : originalProps.top,
        scaleX: uniformScale,
        scaleY: uniformScale,
      });

      console.log('Applied scaling to new image:', {
        originalScale: { x: originalProps.scaleX, y: originalProps.scaleY },
        newScale: { x: uniformScale, y: uniformScale },
        position: { left: adjustedLeft, top: adjustedTop }
      });
    } else {
      // Apply all preserved properties as-is
      newImage.set({
        ...originalProps,
        left: adjustedLeft !== undefined ? adjustedLeft : originalProps.left,
        top: adjustedTop !== undefined ? adjustedTop : originalProps.top,
      });
    }

    // Apply filters if they existed on the original image
    if (originalProps.filters && originalProps.filters.length > 0) {
      newImage.filters = originalProps.filters;
      newImage.applyFilters();
    }

    // Set custom ID
    (newImage as any).id = originalProps.id;

    // Ensure coordinates are properly set after dimension changes
    newImage.setCoords();

    // Applied properties with correct selection boundaries - logging removed
  };

  // Helper function to check if two images match by properties
  const doImagesMatch = (obj1: any, obj2Props: any, tolerance: number = 2) => {
    const positionMatch = Math.abs(obj1.left - obj2Props.left) < tolerance &&
                         Math.abs(obj1.top - obj2Props.top) < tolerance;
    const sizeMatch = Math.abs((obj1.width * obj1.scaleX) - (obj2Props.width * obj2Props.scaleX)) < tolerance &&
                     Math.abs((obj1.height * obj1.scaleY) - (obj2Props.height * obj2Props.scaleY)) < tolerance;
    return positionMatch && sizeMatch;
  };

  // Component mount logging
  useEffect(() => {
    // CustomizationEditor mounted - logging removed

    if (isPublicCustomization) {
      console.log('🔒 PUBLIC CUSTOMIZATION MODE ACTIVE - All changes are temporary and will NOT affect the original template');
    }

    // Try to parse JSON structure
    if (templateData.json) {
      try {
        const parsedJson = JSON.parse(templateData.json);
        // Template JSON structure - logging removed
      } catch (error) {
        // Error parsing template JSON - logging removed
      }
    }
  }, [isPublicCustomization]);

  // Create a deep copy of template data to ensure isolation from original template
  // CRITICAL: This prevents public customization from affecting the original template
  const isolatedTemplateJson = useMemo(() => {
    try {
      // Parse and re-stringify to create a deep copy
      const parsed = JSON.parse(templateData.json);

      // In public customization mode, add extra isolation by creating a completely new object
      if (isPublicCustomization) {
        console.log('🔒 PUBLIC MODE: Creating isolated template copy to prevent original template modification');
        // Deep clone all objects to ensure no references to original data
        const deepCloned = JSON.parse(JSON.stringify(parsed));
        return JSON.stringify(deepCloned);
      }

      return JSON.stringify(parsed);
    } catch (error) {
      console.error('Error creating isolated template copy:', error);
      return templateData.json;
    }
  }, [templateData.json, isPublicCustomization]);

  // Initialize editor first with isolated template data
  const { init, editor } = useEditor({
    defaultState: isolatedTemplateJson,
    defaultWidth: templateData.width,
    defaultHeight: templateData.height,
    clearSelectionCallback: () => {
      onLayerActivation?.(null);
    },
    saveCallback: isPublicCustomization ? () => {
      // PUBLIC CUSTOMIZATION MODE: Only generate preview - NEVER save to database
      // This ensures public customization doesn't affect the original template
      setTimeout(() => {
        generatePreview();
      }, 100);
    } : () => {
      // TEMPLATE CREATOR MODE: Generate preview when canvas changes
      setTimeout(() => {
        generatePreview();
      }, 100);
    },
  });

  // Debug: Log editor state and notify parent
  useEffect(() => {
    if (editor) {
      // Editor initialized - logging removed

      // Notify parent component that editor is ready
      onEditorReady?.(editor);
    }
  }, [editor, onEditorReady]);

  // Helper function to ensure object interaction properties are correctly set
  const ensureObjectInteractionProperties = useCallback(() => {
    if (!editor?.canvas) return;

    // Ensuring object interaction properties are correctly set - logging removed
    const allObjects = editor.canvas.getObjects();
    const editableLayerIds = templateData.editableLayers.map(l => l.id);

    allObjects.forEach((obj: any) => {
      const isEditable = editableLayerIds.includes(obj.id);
      const isWorkspace = obj.name === 'clip';

      if (!isEditable && !isWorkspace) {
        // Lock non-editable objects
        obj.selectable = false;
        obj.evented = false;
        obj.hasControls = false;
        obj.hasBorders = false;
        obj.lockMovementX = true;
        obj.lockMovementY = true;
        obj.lockRotation = true;
        obj.lockScalingX = true;
        obj.lockScalingY = true;
        obj.lockUniScaling = true;
        obj.lockSkewingX = true;
        obj.lockSkewingY = true;

        console.log(`🔒 Re-locked object: ${obj.type} (ID: ${obj.id})`);
      } else if (isEditable) {
        // Ensure editable objects are interactive
        obj.selectable = true;
        obj.evented = true;
        obj.hasControls = true;
        obj.hasBorders = true;
        obj.lockMovementX = false;
        obj.lockMovementY = false;
        obj.lockRotation = false;
        obj.lockScalingX = false;
        obj.lockScalingY = false;
        obj.lockUniScaling = false;
        obj.lockSkewingX = false;
        obj.lockSkewingY = false;

        console.log(`🔓 Re-enabled interaction for object: ${obj.type} (ID: ${obj.id})`);
      }
    });

    editor.canvas.renderAll();
  }, [editor, templateData.editableLayers]);

  // Helper method to apply processed image without permanently modifying template
  const applyProcessedImageToCustomization = useCallback(async (processedImageUrl: string, originalObject: any) => {
    if (!editor?.canvas || !processedImageUrl) {
      console.error('Canvas or processed image URL not available');
      return;
    }

    try {
      console.log('🎨 Applying processed image to customization (non-destructive)');
      console.log('Original object:', {
        type: originalObject.type,
        id: (originalObject as any).id,
        left: originalObject.left,
        top: originalObject.top,
        width: originalObject.width,
        height: originalObject.height,
        scaleX: originalObject.scaleX,
        scaleY: originalObject.scaleY,
        angle: originalObject.angle
      });

      // Check if we need to proxy the image URL (for external URLs)
      let imageUrlToUse = processedImageUrl;
      const needsProxy = processedImageUrl.includes('api.together.ai') ||
                        processedImageUrl.includes('fal.ai') ||
                        processedImageUrl.includes('replicate.delivery') ||
                        processedImageUrl.includes('replicate.com') ||
                        processedImageUrl.includes('stability.ai') ||
                        processedImageUrl.includes('clipdrop-api.co');

      if (needsProxy) {
        imageUrlToUse = `/api/proxy-image?url=${encodeURIComponent(processedImageUrl)}`;
        console.log('🔄 Using proxy for external image:', {
          original: processedImageUrl.substring(0, 50) + '...',
          proxy: imageUrlToUse
        });
      }

      // Create new image from processed URL with proper loading
      const newImage = await new Promise<fabric.Image>((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error('Image loading timeout'));
        }, 15000); // Increased timeout for proxy requests

        fabric.Image.fromURL(imageUrlToUse, (img: fabric.Image) => {
          clearTimeout(timeoutId);

          if (!img || !img.width || !img.height) {
            reject(new Error('Failed to create valid image from processed URL'));
            return;
          }

          const element = img.getElement() as HTMLImageElement;
          console.log('New image loaded:', {
            width: img.width,
            height: img.height,
            naturalWidth: element?.naturalWidth,
            naturalHeight: element?.naturalHeight,
            wasProxied: needsProxy
          });

          resolve(img);
        }, {
          crossOrigin: 'anonymous'
        });
      });

      // Preserve all original properties with better handling
      const originalProps = preserveImageProperties(originalObject);

      console.log('Preserved original properties:', originalProps);

      // Get the intended boundaries from the layer definition (not current object state)
      let targetDisplayWidth = originalProps.width * originalProps.scaleX;
      let targetDisplayHeight = originalProps.height * originalProps.scaleY;

      // Try to get the original intended boundaries from the layer's originalValue
      const objectLayerId = (originalObject as any).id;
      const layer = templateData?.editableLayers?.find(l => l.id === objectLayerId);
      if (layer?.originalValue) {
        try {
          const originalImageProps = JSON.parse(layer.originalValue);
          if (originalImageProps.width && originalImageProps.height) {
            // Use the original intended display size from when the layer was created
            targetDisplayWidth = originalImageProps.width * (originalImageProps.scaleX || 1);
            targetDisplayHeight = originalImageProps.height * (originalImageProps.scaleY || 1);
            console.log('Using original intended boundaries from layer definition:', {
              originalImageProps,
              targetDisplayWidth,
              targetDisplayHeight
            });
          }
        } catch (e) {
          console.log('Could not parse layer originalValue, using current object dimensions');
        }
      }

      // Get the new image's natural dimensions
      const newImageWidth = newImage.width || 1;
      const newImageHeight = newImage.height || 1;

      console.log('Size calculation for replacement:', {
        targetDisplayWidth,
        targetDisplayHeight,
        newImageWidth,
        newImageHeight,
        usingLayerDefinition: !!layer?.originalValue
      });

      // Simple and reliable scaling: fit the new image exactly within the target boundaries
      // This ensures the whole uploaded image is visible without any cropping
      const scaleToFitX = targetDisplayWidth / newImageWidth;
      const scaleToFitY = targetDisplayHeight / newImageHeight;

      // Use the smaller scale factor to ensure the entire image fits (fit mode)
      // This maintains aspect ratio and prevents any cropping
      const uniformScale = Math.min(scaleToFitX, scaleToFitY);

      // Ensure scale is reasonable (prevent extreme values)
      const finalScale = Math.max(0.001, Math.min(100, uniformScale));

      console.log('Boundary-aware scaling for image replacement:', {
        targetBoundaries: { width: targetDisplayWidth, height: targetDisplayHeight },
        newImageSize: { width: newImageWidth, height: newImageHeight },
        scaleToFitX: scaleToFitX.toFixed(3),
        scaleToFitY: scaleToFitY.toFixed(3),
        uniformScale: uniformScale.toFixed(3),
        finalScale: finalScale.toFixed(3),
        resultSize: {
          width: (newImageWidth * uniformScale).toFixed(1),
          height: (newImageHeight * uniformScale).toFixed(1)
        },
        scalingBehavior: uniformScale > 1 ? 'SCALING UP (small image)' : 'SCALING DOWN (large image)',
        aspectRatioPreserved: true,
        wholeImageVisible: true,
        fitsWithinTargetBoundaries: (newImageWidth * finalScale) <= targetDisplayWidth && (newImageHeight * finalScale) <= targetDisplayHeight
      });

      // Apply all preserved properties with uniform scaling to maintain aspect ratio
      newImage.set({
        left: originalProps.left,
        top: originalProps.top,
        scaleX: finalScale,
        scaleY: finalScale,
        angle: originalProps.angle,
        flipX: originalProps.flipX,
        flipY: originalProps.flipY,
        opacity: originalProps.opacity,
        visible: originalProps.visible,
        selectable: originalProps.selectable,
        evented: originalProps.evented,
      });

      // Preserve the layer ID using direct assignment
      if (originalProps.id) {
        (newImage as any).id = originalProps.id;
        console.log('Preserved layer ID:', originalProps.id);
      } else {
        console.warn('Original object had no ID, this may cause interaction issues');
      }

      console.log('Applied properties to new image:', {
        left: newImage.left,
        top: newImage.top,
        scaleX: newImage.scaleX,
        scaleY: newImage.scaleY,
        angle: newImage.angle,
        id: (newImage as any).id,
        actualDisplayWidth: (newImage.width || 0) * (newImage.scaleX || 1),
        actualDisplayHeight: (newImage.height || 0) * (newImage.scaleY || 1)
      });

      // Get the layer index to maintain order
      const objectIndex = editor.canvas.getObjects().indexOf(originalObject);
      console.log('Replacing at layer index:', objectIndex);

      // Remove original and add new image at same position
      editor.canvas.remove(originalObject);

      if (objectIndex !== -1) {
        editor.canvas.insertAt(newImage, objectIndex, false);
      } else {
        editor.canvas.add(newImage);
      }

      // Ensure coordinates are properly set
      newImage.setCoords();

      // Select the new image and render
      editor.canvas.setActiveObject(newImage);
      editor.canvas.renderAll();

      // Ensure the new image has correct interaction properties
      setTimeout(() => {
        ensureObjectInteractionProperties();
      }, 100);

      console.log('✅ Processed image applied successfully to customization with proper sizing');
      return newImage;

    } catch (error) {
      console.error('❌ Error applying processed image to customization:', error);
      throw error;
    }
  }, [editor, preserveImageProperties, ensureObjectInteractionProperties]);

  // Helper method to send image to remove background API (defined first)
  const sendToRemoveBackgroundAPI = useCallback(async (imageDataUrl: string, originalObject: any) => {
    try {
      console.log('Sending image to remove background API...');
      console.log('Public customization mode:', isPublicCustomization);

      // Use different API endpoints based on mode
      const apiEndpoint = isPublicCustomization ? '/api/public/ai/remove-bg' : '/api/ai/remove-bg';
      console.log('Using API endpoint:', apiEndpoint);

      // Call the remove background API
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: imageDataUrl,
          provider: 'clipdrop' // Default provider
        }),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.data && editor?.canvas) {
        console.log('Background removal successful, applying to customization');

        // Use the new non-destructive approach for customization
        await applyProcessedImageToCustomization(result.data, originalObject);

        console.log('Background removal successfully applied to customization');
      } else {
        console.error('No result data or canvas not available');
      }
    } catch (error) {
      console.error('API call failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`Background removal failed: ${errorMessage}`);
    } finally {
      setIsRemovingBackground(false);
      setActiveTool("select");
    }
  }, [editor, applyProcessedImageToCustomization]);

  // Handle remove background functionality (defined after helper method)
  const handleRemoveBackground = useCallback(async () => {
    console.log('handleRemoveBackground called');
    if (!editor?.canvas || isRemovingBackground) return;

    const selectedObject = editor.canvas.getActiveObject();
    console.log('Selected object:', selectedObject);
    if (!selectedObject || selectedObject.type !== 'image') {
      alert('Please select an image to remove its background');
      return;
    }

    console.log('Starting background removal process');
    setIsRemovingBackground(true);

    try {
      // Always convert the image to base64 to avoid blob URL issues
      console.log('Converting selected image to base64...');

      // Get the image element from the selected object
      let imageElement = (selectedObject as any)._originalElement || (selectedObject as any)._element;

      if (!imageElement) {
        // Try to get the image from the src property
        const imageUrl = (selectedObject as any).src;
        if (imageUrl) {
          console.log('Loading image from URL:', imageUrl);
          const img = new Image();
          img.crossOrigin = 'anonymous';

          img.onload = () => {
            console.log('Image loaded successfully');
            // Create canvas to convert to base64
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) {
              throw new Error('Could not create canvas context');
            }

            canvas.width = img.naturalWidth || img.width;
            canvas.height = img.naturalHeight || img.height;
            ctx.drawImage(img, 0, 0);

            const imageDataUrl = canvas.toDataURL('image/png');
            console.log('Generated data URL length:', imageDataUrl.length);

            // Now send to API
            sendToRemoveBackgroundAPI(imageDataUrl, selectedObject);
          };

          img.onerror = (error) => {
            console.error('Failed to load image:', error);
            alert('Failed to load image for processing');
            setIsRemovingBackground(false);
            setActiveTool("select");
          };

          img.src = imageUrl;
          return; // Exit here since the actual API call is in the callback
        } else {
          throw new Error('Could not get image element or URL');
        }
      }

      // If we have an image element, convert it directly
      console.log('Converting image element to base64...');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Could not create canvas context');
      }

      canvas.width = imageElement.naturalWidth || imageElement.width;
      canvas.height = imageElement.naturalHeight || imageElement.height;
      ctx.drawImage(imageElement, 0, 0);

      const imageDataUrl = canvas.toDataURL('image/png');
      console.log('Generated data URL length:', imageDataUrl.length);

      // Now send to API
      sendToRemoveBackgroundAPI(imageDataUrl, selectedObject);

      return; // Exit here since the actual API call is in the callback
    } catch (error) {
      console.error('Background removal failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`Background removal failed: ${errorMessage}`);
      setIsRemovingBackground(false);
      setActiveTool("select");
    }
  }, [editor, isRemovingBackground, sendToRemoveBackgroundAPI]);

  // Handle AI edit functionality (using the same working logic as main template creator)
  const handleAiEdit = useCallback(async (layerId: string, prompt: string) => {
    console.log('handleAiEdit called for layer:', layerId, 'with prompt:', prompt);
    if (!editor?.canvas || isAiEditing) return;

    // Find the canvas object with the matching layer ID and select it
    const canvasObjects = editor.canvas.getObjects();
    const targetObject = canvasObjects.find((obj: any) => obj.id === layerId);

    if (!targetObject || targetObject.type !== 'image') {
      alert('Please select an image layer first');
      return;
    }

    // Select the target object so getSelectedImageUrlForAI can find it
    editor.canvas.setActiveObject(targetObject);
    editor.canvas.renderAll();

    // Use the same working logic as the main template creator
    const { getSelectedImageUrlForAI, applyProcessedImageToSelected } = await import('@/fabric/fabric-utils');

    const imageUrl = getSelectedImageUrlForAI(editor.canvas);
    if (!imageUrl) {
      alert('Could not get image data from the selected object');
      return;
    }

    console.log('AI editing image with prompt:', prompt);
    setIsAiEditing(true);

    try {
      // Call the public API endpoint (no authentication required)
      // This ensures public customization doesn't affect the original template
      const response = await fetch('/api/public/ai/edit-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: imageUrl,
          prompt: prompt.trim(),
          model: 'black-forest-labs/FLUX.1-kontext-dev', // Use Kontext model for image-to-image
          width: 1024,
          height: 1024,
          steps: 15, // Reduced from 20 for faster generation
          guidance: 6.0 // Reduced from 7.5 for faster generation
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API request failed: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.data && editor?.canvas) {
        console.log('AI edit successful, applying to customization');

        // Use the new non-destructive approach for customization
        await applyProcessedImageToCustomization(result.data, targetObject);

        console.log('AI edit successfully applied to customization');

        // Update the customizations state as well
        onCustomizationChange(layerId, result.data);
      } else {
        throw new Error('No edited image data received');
      }
    } catch (error) {
      console.error('AI edit failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`AI editing failed: ${errorMessage}`);
    } finally {
      setIsAiEditing(false);
    }
  }, [editor, isAiEditing, onCustomizationChange, applyProcessedImageToCustomization]);

  // Handle tool changes (defined after editor initialization)
  const handleToolChange = useCallback((tool: ActiveTool) => {
    if (tool === "remove-bg") {
      handleRemoveBackground();
    } else if (tool === "pan") {
      // Disable any other modes first
      if (activeTool !== "pan") {
        editor?.disableDrawingMode?.();
      }
      editor?.enablePanMode();
      setActiveTool(tool);
    } else if (tool === "select") {
      // Enable select mode by disabling other modes
      if (activeTool === "pan") {
        editor?.disablePanMode();
      }
      editor?.disableDrawingMode?.();
      // Ensure canvas selection is enabled
      if (editor?.canvas) {
        editor.canvas.selection = true;
        editor.canvas.defaultCursor = 'default';
        editor.canvas.hoverCursor = 'move';
        editor.canvas.moveCursor = 'move';
      }
      setActiveTool(tool);
    } else {
      // Disable pan mode when switching to other tools
      if (activeTool === "pan") {
        editor?.disablePanMode();
      }
      setActiveTool(tool);
    }
  }, [handleRemoveBackground, editor, activeTool]);

  // Optimized preview generation with debouncing
  const generatePreview = useMemo(() => {
    return debounce(() => {
      if (!editor?.canvas) return;

      try {
        // Save current viewport transform
        const currentTransform = editor.canvas.viewportTransform?.slice();

        // Reset viewport transform to get the full canvas
        editor.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

        // Get all visible content objects (excluding workspace)
        const contentObjects = editor.canvas.getObjects().filter((obj: any) =>
          obj.name !== "clip" && obj.visible !== false && obj.opacity > 0
        );

        let dataUrl: string;

        if (contentObjects.length > 0) {
          // Calculate tight bounding box of all content objects
          let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

          contentObjects.forEach((obj: any) => {
            const bounds = obj.getBoundingRect();
            minX = Math.min(minX, bounds.left);
            minY = Math.min(minY, bounds.top);
            maxX = Math.max(maxX, bounds.left + bounds.width);
            maxY = Math.max(maxY, bounds.top + bounds.height);
          });

          const boundingWidth = maxX - minX;
          const boundingHeight = maxY - minY;

          // Add small padding
          const padding = 5;
          const finalLeft = Math.max(0, minX - padding);
          const finalTop = Math.max(0, minY - padding);
          const finalWidth = boundingWidth + (padding * 2);
          const finalHeight = boundingHeight + (padding * 2);

          // Use standard fabric.js toDataURL
          dataUrl = editor.canvas.toDataURL({
            format: 'jpeg',
            quality: 0.8,
            multiplier: 0.4,
            left: finalLeft,
            top: finalTop,
            width: finalWidth,
            height: finalHeight,
          });
        } else {
          // Fallback to full canvas
          dataUrl = editor.canvas.toDataURL({
            format: 'jpeg',
            quality: 0.8,
            multiplier: 0.4,
          });
        }

        // Restore viewport transform
        if (currentTransform) {
          editor.canvas.setViewportTransform(currentTransform);
        }

        // Force a render to ensure canvas state is correct
        editor.canvas.renderAll();

        // Ensure object interaction properties are preserved after preview generation
        const allObjects = editor.canvas.getObjects();
        const editableLayerIds = templateData.editableLayers.map(l => l.id);

        allObjects.forEach((obj: any) => {
          const isEditable = editableLayerIds.includes(obj.id);
          const isWorkspace = obj.name === 'clip';

          if (isEditable) {
            // Ensure editable objects remain interactive
            obj.selectable = true;
            obj.evented = true;
            obj.hasControls = true;
            obj.hasBorders = true;
          } else if (!isWorkspace) {
            // Ensure non-editable objects remain locked
            obj.selectable = false;
            obj.evented = false;
            obj.hasControls = false;
            obj.hasBorders = false;
          }
        });

        if (dataUrl) {
          onPreviewGenerated(dataUrl);
        }
      } catch (error) {
        console.error('Failed to generate preview:', error);
      }
    }, 300); // Debounce for better responsiveness
  }, [editor, onPreviewGenerated, templateData.editableLayers]);

  // Generate high-quality download from canvas
  const generateDownload = useCallback((filename: string, quality: number = 1.0, format: string = 'png') => {
    if (!editor?.canvas) {
      // No canvas available for download - logging removed
      return;
    }

    try {
      // Generating high-quality download - logging removed

      // Save current viewport transform
      const currentTransform = editor.canvas.viewportTransform?.slice();

      // Reset viewport transform to get the full canvas
      editor.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

      // Get all visible content objects (excluding workspace)
      const contentObjects = editor.canvas.getObjects().filter((obj: any) =>
        obj.name !== "clip" && obj.visible !== false && obj.opacity > 0
      );

      let dataUrl: string;

      if (contentObjects.length > 0) {
        // Calculate tight bounding box of all content objects
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

        contentObjects.forEach((obj: any) => {
          const bounds = obj.getBoundingRect();
          minX = Math.min(minX, bounds.left);
          minY = Math.min(minY, bounds.top);
          maxX = Math.max(maxX, bounds.left + bounds.width);
          maxY = Math.max(maxY, bounds.top + bounds.height);
        });

        const boundingWidth = maxX - minX;
        const boundingHeight = maxY - minY;

        // Add small padding to ensure content isn't cut off
        const padding = 10;
        const finalLeft = Math.max(0, minX - padding);
        const finalTop = Math.max(0, minY - padding);
        const finalWidth = boundingWidth + (padding * 2);
        const finalHeight = boundingHeight + (padding * 2);

        console.log('Content bounds (tight crop):', {
          left: finalLeft,
          top: finalTop,
          width: finalWidth,
          height: finalHeight,
          contentObjects: contentObjects.length
        });

        dataUrl = editor.canvas.toDataURL({
          format: format,
          quality: quality,
          multiplier: 2, // Higher resolution for download
          left: finalLeft,
          top: finalTop,
          width: finalWidth,
          height: finalHeight,
        });
      } else {
        // Fallback to full canvas
        console.log('Using full canvas for download');
        dataUrl = editor.canvas.toDataURL({
          format: format,
          quality: quality,
          multiplier: 2,
        });
      }

      // Restore viewport transform
      if (currentTransform) {
        editor.canvas.setViewportTransform(currentTransform);
      }

      console.log('Generated dataURL length:', dataUrl.length);

      // Trigger download
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('Download triggered successfully for file:', filename);
    } catch (error) {
      console.error('Failed to generate download:', error);
    }
  }, [editor]);

  // Expose download function globally for debugging (after it's defined)
  useEffect(() => {
    if (editor) {
      (window as any).debugDownload = () => {
        // Dispatch the same event as the download button
        const event = new CustomEvent('downloadCustomized', {
          detail: {
            filename: 'debug-download.png',
            quality: 1.0,
            format: 'png'
          }
        });
        window.dispatchEvent(event);
      };
    }
  }, [editor]);

  // Initialize canvas (same as main editor)
  useEffect(() => {
    let retryCount = 0;
    const maxRetries = 10;

    const initializeCanvas = () => {
      if (!containerRef.current || !canvasRef.current) {
        console.log('Container or canvas ref not available');
        return false;
      }

      // Check if canvas element is still in the DOM
      if (!document.contains(canvasRef.current)) {
        console.log('Canvas element not in DOM, retrying...');
        return false;
      }

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      console.log(`Canvas init attempt ${retryCount + 1}/${maxRetries}:`, { containerWidth, containerHeight });

      if (containerWidth === 0 || containerHeight === 0) {
        console.log('Container has zero dimensions, retrying...');
        return false;
      }

      // Ensure reasonable dimensions with both minimum and maximum bounds
      const finalWidth = Math.min(Math.max(containerWidth, 300), 3000);
      const finalHeight = Math.min(Math.max(containerHeight, 200), 2000);

      try {
        const canvas = new fabric.Canvas(canvasRef.current, {
          controlsAboveOverlay: true,
          preserveObjectStacking: true,
          width: finalWidth,
          height: finalHeight,
        });

        console.log('Canvas initialized with dimensions:', canvas.getWidth(), 'x', canvas.getHeight());

        init({
          initialCanvas: canvas,
          initialContainer: container,
        });

        return canvas;
      } catch (error) {
        console.error('Error initializing canvas:', error);
        return false;
      }
    };

    const attemptInit = () => {
      const canvas = initializeCanvas();

      if (!canvas && retryCount < maxRetries) {
        retryCount++;
        const delay = Math.min(100 * retryCount, 1000); // Progressive delay up to 1 second
        // Canvas init failed, retrying - logging removed
        setTimeout(attemptInit, delay);
      } else if (!canvas) {
        // Failed to initialize canvas after maximum retries - logging removed
        setIsCanvasLoading(false);
      }

      return canvas;
    };

    // Start initialization
    const canvas = attemptInit();

    return () => {
      if (canvas) {
        try {
          canvas.dispose();
        } catch (error) {
          // Error disposing canvas - logging removed
        }
      }
    };
  }, [init]);

  // Handle canvas dimension changes (separate from autoZoom to prevent loops)
  useEffect(() => {
    if (!containerRef.current || !editor?.canvas) return;

    let resizeTimeout: NodeJS.Timeout;
    const resizeObserver = new ResizeObserver((entries) => {
      // Throttle resize events to prevent excessive updates
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect;
          // Add bounds checking to prevent unreasonable dimensions
          if (width > 0 && height > 0 && width < 5000 && height < 5000 && editor?.canvas) {
            const currentWidth = editor.canvas.getWidth();
            const currentHeight = editor.canvas.getHeight();

            // Only update if dimensions actually changed significantly
            if (Math.abs(currentWidth - width) > 5 || Math.abs(currentHeight - height) > 5) {
              editor.canvas.setDimensions({ width, height });
            }
          }
        }
      }, 100); // 100ms throttle
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      clearTimeout(resizeTimeout);
      resizeObserver.disconnect();
    };
  }, [editor]);

  // Initialize canvas loading state without auto-zoom interference
  useEffect(() => {
    if (!editor?.canvas || !isCanvasLoading) return;

    console.log('Editor is ready, checking for content');

    // Wait for canvas to be fully loaded with content
    const checkForContent = () => {
      const objects = editor.canvas.getObjects();
      console.log(`Canvas has ${objects.length} objects, checking for content...`);

      if (objects.length > 1) { // More than just the workspace
        console.log('Canvas has content, clearing loading state');

        // Ensure object interaction properties are correctly set after loading
        setTimeout(() => {
          ensureObjectInteractionProperties();
        }, 100);

        setIsCanvasLoading(false);
      } else {
        // If no objects yet, wait a bit more (max 5 seconds)
        const maxRetries = 25; // 25 * 200ms = 5 seconds
        const currentRetry = (checkForContent as any).retryCount || 0;

        if (currentRetry < maxRetries) {
          (checkForContent as any).retryCount = currentRetry + 1;
          setTimeout(checkForContent, 200);
        } else {
          console.warn('Template loading timeout - no content found after 5 seconds');
          setIsCanvasLoading(false);
        }
      }
    };

    // Small delay to ensure canvas is ready
    setTimeout(checkForContent, 300);
  }, [editor, isCanvasLoading]);

  // Fallback timeout to clear loading state
  useEffect(() => {
    const fallbackTimeout = setTimeout(() => {
      if (isCanvasLoading) {
        console.warn('Fallback: Clearing loading state after 10 seconds');
        setIsCanvasLoading(false);
      }
    }, 10000); // 10 seconds fallback

    return () => clearTimeout(fallbackTimeout);
  }, [isCanvasLoading]);

  // Periodic check to ensure object interaction properties remain correct
  useEffect(() => {
    if (!editor?.canvas || isCanvasLoading) return;

    const intervalId = setInterval(() => {
      // Additional safety check inside the interval
      if (editor?.canvas) {
        ensureObjectInteractionProperties();
      }
    }, 5000); // Check every 5 seconds

    return () => clearInterval(intervalId);
  }, [editor, isCanvasLoading, ensureObjectInteractionProperties]);

  // Handle selection changes to notify parent
  useEffect(() => {
    if (!editor?.canvas) return;

    const handleSelectionCreated = (e: fabric.IEvent) => {
      const target = e.target;
      if (target) {
        const layerId = (target as any).id;
        const layer = templateData.editableLayers.find(l => l.id === layerId);

        // Only allow selection of editable layers
        if (layer) {
          console.log('Canvas object selected:', layerId);
          onLayerActivation?.(layerId);

          // Enhance visual feedback for selected object
          target.set({
            borderColor: '#facc15',
            cornerColor: '#facc15',
            cornerSize: 8,
            transparentCorners: false,
            borderScaleFactor: 2,
          });
          editor.canvas.renderAll();
        } else {
          // If a non-editable object was somehow selected, clear the selection
          console.log('Preventing selection of non-editable object:', target.type, layerId);
          editor.canvas.discardActiveObject();
          editor.canvas.renderAll();
        }
      }
    };

    const handleSelectionCleared = () => {
      console.log('Canvas selection cleared');
      onLayerActivation?.(null);
    };

    const handleTextChanged = (e: fabric.IEvent) => {
      const target = e.target;
      if (target && target.type === 'textbox') {
        // Skip if this update is coming from the sidebar to prevent circular updates
        if ((target as any)._isUpdatingFromSidebar) {
          return;
        }

        const layerId = (target as any).id;
        const layer = templateData.editableLayers.find(l => l.id === layerId);
        if (layer && layer.type === 'text') {
          const currentText = (target as fabric.Textbox).text || '';
          onCustomizationChange(layerId, currentText);
        }
      }
    };

    const handleMouseDown = (e: fabric.IEvent) => {
      const target = e.target;
      if (target) {
        const layerId = (target as any).id;
        const isEditable = templateData.editableLayers.some(l => l.id === layerId);
        const isWorkspace = (target as any).name === 'clip';

        // Prevent interaction with non-editable objects
        if (!isEditable && !isWorkspace) {
          console.log('Preventing interaction with locked object:', target.type, layerId);
          editor.canvas.discardActiveObject();
          editor.canvas.renderAll();
          if (e.e) {
            e.e.preventDefault();
            e.e.stopPropagation();
          }
          return false;
        }
      }
    };

    editor.canvas.on('selection:created', handleSelectionCreated);
    editor.canvas.on('selection:updated', handleSelectionCreated);
    editor.canvas.on('selection:cleared', handleSelectionCleared);
    editor.canvas.on('text:changed', handleTextChanged);
    editor.canvas.on('mouse:down', handleMouseDown);

    return () => {
      editor.canvas.off('selection:created', handleSelectionCreated);
      editor.canvas.off('selection:updated', handleSelectionCreated);
      editor.canvas.off('selection:cleared', handleSelectionCleared);
      editor.canvas.off('text:changed', handleTextChanged);
      editor.canvas.off('mouse:down', handleMouseDown);
    };
  }, [editor, templateData.editableLayers, onLayerActivation, onCustomizationChange]);

  // Debounced function to apply customizations
  const applyCustomizations = useCallback(
    debounce(() => {
      if (!editor?.canvas || isApplyingCustomizations.current) return;

      isApplyingCustomizations.current = true;
      console.log('Applying customizations:', customizations);
      console.log('Template editable layers:', templateData.editableLayers.map(l => ({ id: l.id, type: l.type, originalValue: l.originalValue })));

      templateData.editableLayers.forEach(layer => {
      const customValue = customizations[layer.id];
      console.log(`Processing layer ${layer.id} (${layer.type}):`, customValue);

      // Skip if no custom value or if it's the same as original value
      if (!customValue || customValue === layer.originalValue) {
        console.log(`Skipping layer ${layer.id} - no custom value or same as original`);
        return;
      }

      // First try to find by ID
      let canvasObject = editor.canvas.getObjects().find((obj: any) => obj.id === layer.id);

      // If not found by ID, try to find using the same matching logic as during initialization
      if (!canvasObject && layer.type === 'image' && layer.originalValue) {
        console.log(`Canvas object not found by ID for layer ${layer.id}, trying property matching...`);

        try {
          const originalProps = JSON.parse(layer.originalValue);
          const allObjects = editor.canvas.getObjects();
          const imageObjects = allObjects.filter((obj: any) => obj.type === 'image');

          // Try exact position and size match
          canvasObject = imageObjects.find((obj: any) => doImagesMatch(obj, originalProps, 2));

          if (canvasObject) {
            console.log(`Found canvas object by property matching for layer ${layer.id}`);
            // Assign the ID for future lookups
            (canvasObject as any).id = layer.id;
          }
        } catch (e) {
          console.warn(`Failed to parse originalValue for canvas object lookup:`, e);
        }
      }

      if (!canvasObject) {
        console.log(`Canvas object not found for layer ${layer.id}`);
        return;
      }

      if (layer.type === 'text' && canvasObject.type === 'textbox') {
        const textbox = canvasObject as fabric.Textbox;
        console.log(`Current text: "${textbox.text}", New text: "${customValue}"`);

        if (customValue !== undefined && textbox.text !== customValue) {
          console.log(`Updating text for ${layer.id} to: "${customValue}"`);
          // Temporarily disable event handlers to prevent circular updates
          const isUpdatingFromSidebar = true;
          (textbox as any)._isUpdatingFromSidebar = isUpdatingFromSidebar;

          textbox.set('text', customValue);
          if (editor?.canvas) {
            editor.canvas.renderAll();
          }

          // Clean up the flag after a short delay
          setTimeout(() => {
            delete (textbox as any)._isUpdatingFromSidebar;
          }, 100);
        }
      } else if (layer.type === 'image' && customValue) {
        console.log(`Replacing image for ${layer.id} with: ${customValue}`);
        console.log(`Custom value type: ${typeof customValue}, starts with {: ${customValue.startsWith('{')}`);

        // Check if customValue is the original JSON properties - if so, skip replacement
        if (customValue === layer.originalValue) {
          console.log(`Skipping image replacement for ${layer.id} - customValue is same as originalValue`);
          return;
        }

        // Extract the image URL from the customValue
        let imageUrl = customValue;

        // If customValue is a JSON string (from stored properties), extract the src
        if (typeof customValue === 'string' && customValue.startsWith('{')) {
          try {
            const imageProps = JSON.parse(customValue);
            if (imageProps.src) {
              imageUrl = imageProps.src;
              console.log(`Extracted image URL from JSON: ${imageUrl}`);
            } else {
              console.warn('No src property found in image JSON, skipping replacement');
              return;
            }
          } catch (e) {
            console.warn('Failed to parse image properties JSON, treating as direct URL:', e);
            // If it's not valid JSON but starts with {, it's probably malformed - skip it
            if (customValue.startsWith('{')) {
              console.warn('Malformed JSON detected, skipping image replacement');
              return;
            }
          }
        }

        // Validate that we have a proper URL
        if (!imageUrl || !imageUrl.trim() || (!imageUrl.startsWith('http') && !imageUrl.startsWith('blob:'))) {
          console.warn(`Invalid image URL detected: ${imageUrl}, skipping replacement`);
          return;
        }

        // Check if this image is already being processed to prevent infinite loops
        const currentSrc = (canvasObject as fabric.Image).getSrc?.();
        if (currentSrc === imageUrl) {
          console.log(`Image ${layer.id} already has the correct source, skipping replacement`);
          return;
        }

        // Additional check: if imageUrl is empty or invalid, skip
        if (!imageUrl || imageUrl.trim() === '') {
          console.log(`Image ${layer.id} has empty URL, skipping replacement`);
          return;
        }

        // Handle image replacement
        fabric.Image.fromURL(imageUrl, (img) => {
          if (!editor.canvas) {
            console.error('Canvas not available during image replacement');
            return;
          }

          if (!img) {
            console.error('Failed to load image from URL:', customValue);
            return;
          }

          console.log('Original image object:', canvasObject);
          console.log('New image loaded:', img);

          // Use helper function to preserve all properties
          const originalProps = preserveImageProperties(canvasObject);

          console.log('Preserving original properties:', originalProps);

          // Get the intended boundaries from the layer definition (not current object state)
          let targetDisplayWidth = (originalProps.width || 100) * (originalProps.scaleX || 1);
          let targetDisplayHeight = (originalProps.height || 100) * (originalProps.scaleY || 1);

          // Try to get the original intended boundaries from the layer's originalValue
          const layerDef = templateData?.editableLayers?.find(l => l.id === layerId);
          if (layerDef?.originalValue) {
            try {
              const originalImageProps = JSON.parse(layerDef.originalValue);
              if (originalImageProps.width && originalImageProps.height) {
                // Use the original intended display size from when the layer was created
                targetDisplayWidth = originalImageProps.width * (originalImageProps.scaleX || 1);
                targetDisplayHeight = originalImageProps.height * (originalImageProps.scaleY || 1);
                console.log('AI Edit: Using original intended boundaries from layer definition:', {
                  originalImageProps,
                  targetDisplayWidth,
                  targetDisplayHeight
                });
              }
            } catch (e) {
              console.log('AI Edit: Could not parse layer originalValue, using current object dimensions');
            }
          }

          // Ensure we have valid dimensions
          if (targetDisplayWidth <= 0 || targetDisplayHeight <= 0 || !img.width || !img.height) {
            console.error('Invalid dimensions detected:', {
              targetDisplayWidth,
              targetDisplayHeight,
              newImageWidth: img.width,
              newImageHeight: img.height
            });
            return;
          }

          // Get the new image's natural dimensions
          const newImageWidth = img.width || 1;
          const newImageHeight = img.height || 1;

          // Simple and reliable scaling: fit the new image exactly within the target boundaries
          const scaleToFitX = targetDisplayWidth / newImageWidth;
          const scaleToFitY = targetDisplayHeight / newImageHeight;

          // Use the smaller scale factor to ensure the entire image fits (fit mode)
          const uniformScale = Math.min(scaleToFitX, scaleToFitY);

          // Ensure scale is reasonable (prevent extreme values)
          const finalScale = Math.max(0.001, Math.min(1000, uniformScale));

          console.log('Boundary-aware scaling for AI edit:', {
            targetBoundaries: { width: targetDisplayWidth, height: targetDisplayHeight },
            newImageSize: { width: newImageWidth, height: newImageHeight },
            scaleToFitX: scaleToFitX.toFixed(3),
            scaleToFitY: scaleToFitY.toFixed(3),
            uniformScale: uniformScale.toFixed(3),
            finalScale: finalScale.toFixed(3),
            resultSize: {
              width: (newImageWidth * finalScale).toFixed(1),
              height: (newImageHeight * finalScale).toFixed(1)
            },
            scalingBehavior: finalScale > 1 ? 'SCALING UP (small image)' : 'SCALING DOWN (large image)',
            aspectRatioPreserved: true,
            wholeImageVisible: true,
            fitsWithinTargetBoundaries: (newImageWidth * finalScale) <= targetDisplayWidth && (newImageHeight * finalScale) <= targetDisplayHeight
          });

          // Keep original position - no centering adjustments
          const adjustedLeft = originalProps.left;
          const adjustedTop = originalProps.top;

          console.log('Position (no adjustment):', {
            originalLeft: originalProps.left,
            originalTop: originalProps.top,
            adjustedLeft,
            adjustedTop,
            scaledWidth: newImageWidth * finalScale,
            scaledHeight: newImageHeight * finalScale
          });

          // Apply all properties with uniform scaling to maintain aspect ratio
          img.set({
            ...originalProps,
            left: adjustedLeft,
            top: adjustedTop,
            scaleX: finalScale,
            scaleY: finalScale
          });

          // Override the ID for this specific layer
          (img as any).id = layer.id;

          // Get the exact position in the layer stack
          const objectIndex = editor.canvas.getObjects().indexOf(canvasObject);
          console.log(`Replacing image at index ${objectIndex}`);

          // Only proceed if we found the object
          if (objectIndex === -1) {
            console.warn(`Cannot find object ${layer.id} in canvas objects`);
            return;
          }

          // Remove old image and insert new one at exact same position
          editor.canvas.remove(canvasObject);
          editor.canvas.insertAt(img, objectIndex, false);

          // Ensure the image is properly initialized before rendering
          try {
            // Update coordinates first
            img.setCoords();

            // Render without controls first
            if (editor?.canvas) {
              editor.canvas.renderAll();
            }

            // Small delay before selecting to avoid the getRetinaScaling error
            setTimeout(() => {
              try {
                // Select the new image to show it's been replaced
                if (editor?.canvas && editor.canvas.getContext() && img.canvas === editor.canvas) {
                  editor.canvas.setActiveObject(img);
                  editor.canvas.renderAll();
                }

                console.log(`Image replaced for ${layer.id} at index ${objectIndex}`, img);

                // Ensure object interaction properties are correctly set after replacement
                setTimeout(() => {
                  ensureObjectInteractionProperties();
                }, 50);
              } catch (selectionError) {
                console.warn('Error selecting new image:', selectionError);
                // Still render the canvas even if selection fails
                if (editor?.canvas) {
                  editor.canvas.renderAll();
                }

                // Still ensure interaction properties are set even if selection fails
                setTimeout(() => {
                  ensureObjectInteractionProperties();
                }, 50);
              }
            }, 100);
          } catch (renderError) {
            console.error('Error during image replacement:', renderError);
            // Fallback: just render the canvas
            if (editor?.canvas) {
              editor.canvas.renderAll();
            }
          }
        }, {
          crossOrigin: 'anonymous'
        });
      }
    });

    // Reset the flag after processing
    isApplyingCustomizations.current = false;

    // Refresh object locking state after customizations are applied
    refreshObjectLocking();

    // Generate preview after customizations are applied
    setTimeout(() => {
      generatePreview();
    }, 200);
    }, 150), // 150ms debounce - faster response for better UX
    [editor, templateData.editableLayers, customizations, generatePreview]
  );

  // Function to refresh object locking state
  const refreshObjectLocking = useCallback(() => {
    if (!editor?.canvas) return;

    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);
    const allObjects = editor.canvas.getObjects();

    allObjects.forEach((obj: any) => {
      const isEditable = editableLayerIds.includes(obj.id);
      const isWorkspace = obj.name === 'clip';

      if (!isEditable && !isWorkspace) {
        // Lock the object - make it non-selectable and non-interactive
        obj.selectable = false;
        obj.evented = false;
        obj.hasControls = false;
        obj.hasBorders = false;
        obj.lockMovementX = true;
        obj.lockMovementY = true;
        obj.lockRotation = true;
        obj.lockScalingX = true;
        obj.lockScalingY = true;
        obj.lockUniScaling = true;
        obj.lockSkewingX = true;
        obj.lockSkewingY = true;
      } else if (isEditable) {
        // Ensure editable objects are interactive
        obj.selectable = true;
        obj.evented = true;
        obj.hasControls = true;
        obj.hasBorders = true;
        // Remove any locks on editable objects
        obj.lockMovementX = false;
        obj.lockMovementY = false;
        obj.lockRotation = false;
        obj.lockScalingX = false;
        obj.lockScalingY = false;
        obj.lockUniScaling = false;
        obj.lockSkewingX = false;
        obj.lockSkewingY = false;
      }
    });

    editor.canvas.renderAll();
  }, [editor, templateData.editableLayers]);

  // Apply customizations from sidebar to canvas
  useEffect(() => {
    applyCustomizations();
  }, [customizations, applyCustomizations]);

  // Listen for download and preview events from parent component
  useEffect(() => {
    const handleDownloadEvent = (event: CustomEvent) => {
      console.log('Download event received in CustomizationEditor:', event.detail);
      const { filename, quality, format } = event.detail;

      // Call generateDownload directly to avoid dependency issues
      if (!editor?.canvas) {
        console.error('No canvas available for download');
        return;
      }

      try {
        console.log('Generating high-quality download...', {
          canvasObjects: editor.canvas.getObjects().length,
          canvasSize: { width: editor.canvas.width, height: editor.canvas.height }
        });

        // Save current viewport transform and zoom
        const currentTransform = editor.canvas.viewportTransform?.slice();
        const currentZoom = editor.canvas.getZoom();

        // Reset viewport transform and zoom to get the full canvas at original scale
        editor.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
        editor.canvas.setZoom(1);

        // Find the workspace (clip) object to get proper dimensions
        const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
        console.log('Workspace found:', !!workspace, workspace ? {
          left: workspace.left,
          top: workspace.top,
          width: workspace.width,
          height: workspace.height,
          scaleX: workspace.scaleX,
          scaleY: workspace.scaleY
        } : 'none');

        let dataUrl: string;

        // Always calculate bounding box of all visible content objects (excluding workspace)
        const contentObjects = editor.canvas.getObjects().filter((obj: any) =>
          obj.name !== "clip" && obj.visible !== false && obj.opacity > 0
        );

        if (contentObjects.length > 0) {
          // Calculate tight bounding box of all content objects
          let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

          contentObjects.forEach((obj: any) => {
            const bounds = obj.getBoundingRect();
            minX = Math.min(minX, bounds.left);
            minY = Math.min(minY, bounds.top);
            maxX = Math.max(maxX, bounds.left + bounds.width);
            maxY = Math.max(maxY, bounds.top + bounds.height);
          });

          const boundingWidth = maxX - minX;
          const boundingHeight = maxY - minY;

          // Add small padding to ensure content isn't cut off
          const padding = 10;
          const finalLeft = Math.max(0, minX - padding);
          const finalTop = Math.max(0, minY - padding);
          const finalWidth = boundingWidth + (padding * 2);
          const finalHeight = boundingHeight + (padding * 2);

          console.log('Content bounds (tight crop):', {
            left: finalLeft,
            top: finalTop,
            width: finalWidth,
            height: finalHeight,
            contentObjects: contentObjects.length
          });

          dataUrl = editor.canvas.toDataURL({
            format: format,
            quality: quality,
            multiplier: 2,
            left: finalLeft,
            top: finalTop,
            width: finalWidth,
            height: finalHeight,
          });
        } else if (workspace) {
          // Fallback to workspace bounds if no content objects found
          const workspaceBounds = workspace.getBoundingRect();
          console.log('Fallback to workspace bounds:', workspaceBounds);

          dataUrl = editor.canvas.toDataURL({
            format: format,
            quality: quality,
            multiplier: 2,
            left: workspaceBounds.left,
            top: workspaceBounds.top,
            width: workspaceBounds.width,
            height: workspaceBounds.height,
          });
        } else {
          // Final fallback to full canvas
          console.log('Using full canvas for download');
          dataUrl = editor.canvas.toDataURL({
            format: format,
            quality: quality,
            multiplier: 2,
          });
        }

        // Restore viewport transform and zoom
        if (currentTransform) {
          editor.canvas.setViewportTransform(currentTransform);
        }
        editor.canvas.setZoom(currentZoom);

        console.log('Generated dataURL length:', dataUrl.length);

        // Trigger download
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('Download triggered successfully for file:', filename);
      } catch (error) {
        console.error('Failed to generate download:', error);
      }
    };

    const handlePreviewEvent = () => {
      console.log('Preview event received in CustomizationEditor');

      // Call generatePreview directly to avoid dependency issues
      if (!editor?.canvas) return;

      try {
        // Save current viewport transform
        const currentTransform = editor.canvas.viewportTransform?.slice();

        // Reset viewport transform to get the full canvas
        editor.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

        // Get all visible content objects (excluding workspace)
        const contentObjects = editor.canvas.getObjects().filter((obj: any) =>
          obj.name !== "clip" && obj.visible !== false && obj.opacity > 0
        );

        let dataUrl: string;

        if (contentObjects.length > 0) {
          // Calculate tight bounding box of all content objects
          let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

          contentObjects.forEach((obj: any) => {
            const bounds = obj.getBoundingRect();
            minX = Math.min(minX, bounds.left);
            minY = Math.min(minY, bounds.top);
            maxX = Math.max(maxX, bounds.left + bounds.width);
            maxY = Math.max(maxY, bounds.top + bounds.height);
          });

          const boundingWidth = maxX - minX;
          const boundingHeight = maxY - minY;

          // Add small padding
          const padding = 5;
          const finalLeft = Math.max(0, minX - padding);
          const finalTop = Math.max(0, minY - padding);
          const finalWidth = boundingWidth + (padding * 2);
          const finalHeight = boundingHeight + (padding * 2);

          dataUrl = editor.canvas.toDataURL({
            format: 'png',
            quality: 0.9,
            multiplier: 0.5,
            left: finalLeft,
            top: finalTop,
            width: finalWidth,
            height: finalHeight,
          });
        } else {
          // Fallback to full canvas
          dataUrl = editor.canvas.toDataURL({
            format: 'png',
            quality: 0.9,
            multiplier: 0.5,
          });
        }

        // Restore viewport transform
        if (currentTransform) {
          editor.canvas.setViewportTransform(currentTransform);
        }

        onPreviewGenerated(dataUrl);
      } catch (error) {
        console.error('Failed to generate preview:', error);
      }
    };

    const handleAiEditEvent = (event: CustomEvent) => {
      console.log('AI edit event received in CustomizationEditor:', event.detail);
      const { layerId, prompt } = event.detail;
      handleAiEdit(layerId, prompt);
    };

    console.log('Setting up event listeners for download and preview');
    window.addEventListener('downloadCustomized', handleDownloadEvent as EventListener);
    window.addEventListener('generatePreview', handlePreviewEvent);
    window.addEventListener('aiEditImage', handleAiEditEvent as EventListener);

    return () => {
      console.log('Cleaning up event listeners');
      window.removeEventListener('downloadCustomized', handleDownloadEvent as EventListener);
      window.removeEventListener('generatePreview', handlePreviewEvent);
      window.removeEventListener('aiEditImage', handleAiEditEvent as EventListener);
    };
  }, [editor, onPreviewGenerated]);

  // Ensure canvas objects have proper IDs when editor loads
  useEffect(() => {
    if (!editor?.canvas) return;

    const canvas = editor.canvas;
    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);

    console.log('Setting up canvas object IDs for editable layers:', editableLayerIds);

    // Wait a bit for the canvas to be fully loaded
    setTimeout(() => {
      const allObjects = canvas.getObjects();
      // Canvas loaded with objects - logging removed

      // Log only editable objects for debugging
      const editableObjects = allObjects.filter((obj: any) =>
        editableLayerIds.includes(obj.id) ||
        obj.type === 'textbox' ||
        obj.type === 'image'
      );
      // Editable/relevant objects - logging removed

      // Try to match objects to editable layers using multiple criteria
      templateData.editableLayers.forEach((layer) => {
        let matchedObject = allObjects.find((obj: any) => obj.id === layer.id);

        if (!matchedObject) {
          if (layer.type === 'text') {
            // Try to find by text content if no ID match
            matchedObject = allObjects.find((obj: any) =>
              obj.type === 'textbox' &&
              obj.text === layer.originalValue &&
              !editableLayerIds.includes(obj.id)
            );
          } else if (layer.type === 'image') {
            // For images, use more sophisticated matching
            const imageObjects = allObjects.filter((obj: any) =>
              obj.type === 'image' && !editableLayerIds.includes(obj.id)
            );

            console.log(`Trying to match image layer ${layer.id} (${layer.name}) with ${imageObjects.length} available image objects`);
            console.log('Available image objects:', imageObjects.map((obj: any) => ({
              id: obj.id,
              left: obj.left,
              top: obj.top,
              width: obj.width,
              height: obj.height,
              scaleX: obj.scaleX,
              scaleY: obj.scaleY,
              uniqueId: createImageUniqueId(obj)
            })));

            // Strategy 1: Try to match by stored properties if available in originalValue
            if (layer.originalValue) {
              try {
                const originalProps = JSON.parse(layer.originalValue);
                console.log(`Using stored properties for matching layer ${layer.id}:`, originalProps);

                // First try exact position and size match
                matchedObject = imageObjects.find((obj: any) => doImagesMatch(obj, originalProps, 2));

                if (matchedObject) {
                  console.log(`Matched image by exact position/size for layer ${layer.id}`);
                } else {
                  // Try matching by unique identifier
                  matchedObject = imageObjects.find((obj: any) => {
                    const objUniqueId = createImageUniqueId(obj);
                    return objUniqueId === originalProps.uniqueId;
                  });

                  if (matchedObject) {
                    console.log(`Matched image by unique identifier for layer ${layer.id}`);
                  } else {
                    // Try matching by source URL if available
                    if (originalProps.src) {
                      matchedObject = imageObjects.find((obj: any) => {
                        const objSrc = (obj as fabric.Image).getSrc?.();
                        return objSrc === originalProps.src;
                      });

                      if (matchedObject) {
                        console.log(`Matched image by source URL for layer ${layer.id}`);
                      }
                    }
                  }
                }
              } catch (e) {
                console.warn(`Failed to parse originalValue for layer ${layer.id}:`, e);
              }
            }

            // Strategy 2: If no property match, try to match by position similarity (looser matching)
            if (!matchedObject) {
              console.log(`Trying position similarity matching for layer ${layer.id}`);

              if (layer.originalValue) {
                try {
                  const originalProps = JSON.parse(layer.originalValue);

                  // Find the closest image by position
                  let closestDistance = Infinity;
                  let closestObject = null;

                  imageObjects.forEach((obj: any) => {
                    const distance = Math.sqrt(
                      Math.pow(obj.left - originalProps.left, 2) +
                      Math.pow(obj.top - originalProps.top, 2)
                    );

                    if (distance < closestDistance) {
                      closestDistance = distance;
                      closestObject = obj;
                    }
                  });

                  // Only use closest match if it's reasonably close (within 50 pixels)
                  if (closestObject && closestDistance < 50) {
                    matchedObject = closestObject;
                    console.log(`Matched image by closest position (distance: ${closestDistance.toFixed(2)}) for layer ${layer.id}`);
                  }
                } catch (e) {
                  // Continue to next strategy
                }
              }
            }

            // Strategy 3: If still no match, try to match by sorted index (as fallback)
            if (!matchedObject) {
              const imageLayerIndex = templateData.editableLayers
                .filter(l => l.type === 'image')
                .indexOf(layer);

              // Sort image objects by their position (top-to-bottom, left-to-right) for consistent ordering
              const sortedImageObjects = imageObjects.sort((a: any, b: any) => {
                const topDiff = a.top - b.top;
                if (Math.abs(topDiff) > 10) return topDiff; // Different rows
                return a.left - b.left; // Same row, sort by left position
              });

              if (sortedImageObjects[imageLayerIndex]) {
                matchedObject = sortedImageObjects[imageLayerIndex];
                console.log(`Matched image by sorted index ${imageLayerIndex} for layer ${layer.id}`);
              }
            }

            // Strategy 4: Last resort - take first available image but warn about it
            if (!matchedObject && imageObjects.length > 0) {
              matchedObject = imageObjects[0];
              console.warn(`FALLBACK: Using first available image for layer ${layer.id} - this may not be correct!`);
            }
          }
        }

        if (matchedObject && !(matchedObject as any).id) {
          console.log(`✅ Assigning ID ${layer.id} to ${layer.type} object:`, {
            type: matchedObject.type,
            left: matchedObject.left,
            top: matchedObject.top,
            width: matchedObject.width,
            height: matchedObject.height
          });
          (matchedObject as any).id = layer.id;
        } else if (matchedObject && (matchedObject as any).id) {
          console.log(`⚠️ Object already has ID: ${(matchedObject as any).id} for layer ${layer.id}`);
        } else if (!matchedObject) {
          console.warn(`❌ Could not find matching object for layer ${layer.id} (${layer.name})`);
        }
      });

      // Lock all non-editable objects in public customization interface
      console.log('Locking non-editable objects...');
      allObjects.forEach((obj: any) => {
        const isEditable = editableLayerIds.includes(obj.id);
        const isWorkspace = obj.name === 'clip';

        if (!isEditable && !isWorkspace) {
          // Lock the object - make it non-selectable and non-interactive
          obj.selectable = false;
          obj.evented = false;
          obj.hasControls = false;
          obj.hasBorders = false;
          obj.lockMovementX = true;
          obj.lockMovementY = true;
          obj.lockRotation = true;
          obj.lockScalingX = true;
          obj.lockScalingY = true;
          obj.lockUniScaling = true;
          obj.lockSkewingX = true;
          obj.lockSkewingY = true;

          console.log(`🔒 Locked non-editable object: ${obj.type} (ID: ${obj.id || 'no-id'})`);
        } else if (isEditable) {
          // Ensure editable objects are interactive
          obj.selectable = true;
          obj.evented = true;
          obj.hasControls = true;
          obj.hasBorders = true;

          console.log(`🔓 Enabled interaction for editable object: ${obj.type} (ID: ${obj.id})`);
        } else if (isWorkspace) {
          console.log(`🏠 Workspace object: ${obj.type} (keeping default properties)`);
        }
      });

      try {
        canvas.renderAll();

        // Ensure object interaction properties are correctly set after customizations
        setTimeout(() => {
          ensureObjectInteractionProperties();
        }, 100);

        // Verify ID assignment was successful
        const verifyIds = () => {
          const allObjects = canvas.getObjects();
          const assignedIds = allObjects.map((obj: any) => obj.id).filter(Boolean);
          const expectedIds = templateData.editableLayers.map(l => l.id);

          console.log('ID Assignment Verification:', {
            expected: expectedIds,
            assigned: assignedIds,
            missing: expectedIds.filter(id => !assignedIds.includes(id))
          });

          // If some IDs are missing, try to reassign them
          const missingIds = expectedIds.filter(id => !assignedIds.includes(id));
          if (missingIds.length > 0) {
            console.warn('Some IDs are missing, attempting to reassign...');
            // This will trigger the ID assignment logic again
            setTimeout(() => {
              if (editor?.canvas) {
                editor.canvas.fire('canvas:after:render');
              }
            }, 100);
          }
        };

        // Verify IDs after a short delay
        setTimeout(verifyIds, 100);

        // Clear loading state after template load and ID assignment
        if (isCanvasLoading) {
          setTimeout(() => {
            console.log('Template loaded and IDs assigned, clearing loading state');
            setIsCanvasLoading(false);
          }, 300);
        }
      } catch (error) {
        console.error('Error rendering canvas after ID assignment:', error);
      }
    }, 500);
  }, [editor, templateData.editableLayers]);

  // Function to select object by layer ID (called from parent)
  useEffect(() => {
    if (!editor?.canvas) {
      console.log('🚫 No editor canvas available for selection');
      return;
    }

    if (isCanvasLoading) {
      console.log('⏳ Canvas still loading, deferring selection');
      return;
    }

    console.log('🔥 External active layer changed:', externalActiveLayerId);
    console.log('🔥 Editor canvas available:', !!editor.canvas);
    console.log('🔥 Canvas objects count:', editor.canvas.getObjects().length);

    // Add a small delay to ensure IDs are assigned
    const selectObject = () => {
      if (externalActiveLayerId) {
        const allObjects = editor.canvas.getObjects();
        console.log('All canvas objects:', allObjects.map((obj: any) => ({ id: obj.id, type: obj.type, name: obj.name })));

        const targetObject = allObjects.find((obj: any) => obj.id === externalActiveLayerId);
        console.log('🔍 Looking for object with ID:', externalActiveLayerId);
        console.log('🔍 Found target object:', targetObject);
        console.log('🔍 Object details:', targetObject ? {
          id: (targetObject as any).id,
          type: targetObject.type,
          left: targetObject.left,
          top: targetObject.top
        } : 'null');

        if (targetObject) {
          console.log('Selecting object in canvas:', (targetObject as any).id);

          // Enhance selection appearance
          targetObject.set({
            borderColor: '#facc15', // Yellow border
            cornerColor: '#facc15',
            cornerSize: 8,
            transparentCorners: false,
            borderScaleFactor: 2,
          });

          editor.canvas.setActiveObject(targetObject);
          editor.canvas.renderAll();

          // Ensure the object is visible by centering it
          const canvasCenter = editor.canvas.getCenter();
          const objectCenter = targetObject.getCenterPoint();
          const zoom = editor.canvas.getZoom();

          // Pan to center the selected object (with smooth animation)
          const currentVpt = editor.canvas.viewportTransform;
          if (currentVpt) {
            const targetX = canvasCenter.left - objectCenter.x * zoom;
            const targetY = canvasCenter.top - objectCenter.y * zoom;

            // Smooth pan animation
            const steps = 10;
            const deltaX = (targetX - currentVpt[4]) / steps;
            const deltaY = (targetY - currentVpt[5]) / steps;

            let step = 0;
            const animate = () => {
              if (step < steps && currentVpt) {
                currentVpt[4] += deltaX;
                currentVpt[5] += deltaY;
                editor.canvas.requestRenderAll();
                step++;
                requestAnimationFrame(animate);
              }
            };
            animate();
          }

        } else {
          console.log('❌ Target object not found for ID:', externalActiveLayerId);
          console.log('🔍 Available object IDs:', allObjects.map((obj: any) => obj.id).filter(Boolean));

          // Retry after a short delay in case IDs are still being assigned
          // But limit retries to avoid infinite loops
          const maxRetries = 10;
          const currentRetry = (selectObject as any).retryCount || 0;

          if (currentRetry < maxRetries) {
            (selectObject as any).retryCount = currentRetry + 1;
            console.log(`🔄 Retrying object selection (attempt ${currentRetry + 1}/${maxRetries})`);
            setTimeout(selectObject, 200 * (currentRetry + 1)); // Exponential backoff
          } else {
            console.error(`🚫 Failed to find object after ${maxRetries} retries`);
          }
        }
      } else {
        // Clear selection if no layer is active
        console.log('Clearing canvas selection');
        editor.canvas.discardActiveObject();
        editor.canvas.renderAll();
      }
    };

    // Reset retry count for new selection
    (selectObject as any).retryCount = 0;

    // Initial attempt
    selectObject();
  }, [editor, externalActiveLayerId, isCanvasLoading]);



  return (
    <div className="customization-editor-container w-full h-full flex flex-col bg-muted">
      <Toolbar
        editor={editor}
        activeTool={activeTool}
        onChangeActiveTool={handleToolChange}
        key={JSON.stringify(editor?.canvas.getActiveObject())}
      />
      <div className="flex-1 flex">
        <div
          className="customization-canvas-container flex-1 bg-muted overflow-hidden relative w-full"
          ref={containerRef}
        >
        <div className={`absolute inset-0 flex items-center justify-center bg-muted/80 z-10 ${isCanvasLoading ? 'block' : 'hidden'}`}>
          <div className="flex flex-col items-center space-y-3 bg-white p-6 rounded-lg shadow-lg">
            <Loader2 className="h-10 w-10 animate-spin text-blue-600" />
            <div className="text-center">
              <p className="text-lg font-medium text-gray-900">Loading Template</p>
              <p className="text-sm text-gray-600">Please wait while we prepare your editor...</p>
              <p className="text-xs text-gray-500 mt-2">
                Debug: {editor ? 'Editor ready' : 'Editor loading'} |
                Objects: {editor?.canvas?.getObjects().length || 0}
              </p>
            </div>
          </div>
        </div>
        <div className={`absolute inset-0 flex items-center justify-center bg-black/50 z-20 ${isRemovingBackground ? 'block' : 'hidden'}`}>
          <div className="flex flex-col items-center space-y-3 bg-white p-6 rounded-lg shadow-lg">
            <Loader2 className="h-10 w-10 animate-spin text-blue-600" />
            <div className="text-center">
              <p className="text-lg font-medium text-gray-900">Removing Background</p>
              <p className="text-sm text-gray-600">Please wait while we process your image...</p>
            </div>
          </div>
        </div>
        <canvas
          key={`canvas-${templateData.id}`}
          ref={canvasRef}
          className="block w-full h-full"
          style={{
            display: 'block',
            margin: '0 auto',
            maxWidth: '100%',
            maxHeight: '100%',
          }}
        />
        </div>
      </div>
      <Footer
        editor={editor}
        activeTool={activeTool}
        onChangeActiveTool={handleToolChange}
      />
    </div>
  );
};

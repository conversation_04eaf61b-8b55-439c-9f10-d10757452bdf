import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { removeBackground } from "@/services/aiService";

const removeBgSchema = z.object({
  image: z.string(),
  provider: z.enum(['fal', 'replicate', 'clipdrop', 'together']).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { image, provider } = removeBgSchema.parse(body);

    console.log('Public background removal request:', {
      hasImage: !!image,
      provider: provider || 'clipdrop'
    });

    // Call the AI service to remove background
    const result = await removeBackground(image, provider || 'clipdrop');

    console.log('Public background removal successful:', {
      resultUrl: result ? result.substring(0, 50) + '...' : 'none'
    });

    return NextResponse.json({ data: result });
  } catch (error) {
    console.error('Public background removal failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      { error: `Background removal failed: ${errorMessage}` },
      { status: 500 }
    );
  }
}

"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Alert<PERSON>riangle,
  Loader,
  Search,
} from "lucide-react";

import { useGetProjects } from "@/features/projects/api/use-get-projects";
import { useDeleteProject } from "@/features/projects/api/use-delete-project";
import { useDuplicateProject } from "@/features/projects/api/use-duplicate-project";
import { useUpdateProject } from "@/features/projects/api/use-update-project";

import { Button } from "@/components/ui/button";
import { useConfirm } from "@/hooks/use-confirm";
import { ProjectCard } from "./project-card";

export const ProjectsSection = () => {
  const [ConfirmDialog, confirm] = useConfirm(
    "Are you sure?",
    "You are about to delete this project.",
  );
  const [updatingProjectId, setUpdatingProjectId] = useState<string | null>(null);

  const duplicateMutation = useDuplicateProject();
  const removeMutation = useDeleteProject();
  const updateMutation = useUpdateProject();
  const router = useRouter();

  const onCopy = (id: string) => {
    duplicateMutation.mutate({ id });
  };

  const onDelete = async (id: string) => {
    const ok = await confirm();

    if (ok) {
      removeMutation.mutate({ id });
    }
  };

  const onTogglePublic = (id: string, isPublic: boolean) => {
    setUpdatingProjectId(id);
    updateMutation.mutate(
      { id, isPublic },
      {
        onSettled: () => {
          setUpdatingProjectId(null);
        },
      }
    );
  };

  const onConfigureTemplate = (id: string) => {
    // Navigate to the editor with template configuration mode
    router.push(`/editor/${id}?mode=template-config`);
  };

  const {
    data,
    status,
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage,
  } = useGetProjects();

  if (status === "pending") {
    return (
      <div className="space-y-4">
        <h3 className="font-semibold text-lg">
          Recent projects
        </h3>
        <div className="flex flex-col gap-y-4 items-center justify-center h-32">
          <Loader className="size-6 animate-spin text-muted-foreground" />
        </div>
      </div>
    )
  }

  if (status === "error") {
    return (
      <div className="space-y-4">
        <h3 className="font-semibold text-lg">
          Recent projects
        </h3>
        <div className="flex flex-col gap-y-4 items-center justify-center h-32">
          <AlertTriangle className="size-6 text-muted-foreground" />
          <p className="text-muted-foreground text-sm">
            Failed to load projects
          </p>
        </div>
      </div>
    )
  }

  if (
    !data.pages.length ||
    !data.pages[0].data.length
  ) {
    return (
      <div className="space-y-4">
        <h3 className="font-semibold text-lg">
          Recent projects
        </h3>
        <div className="flex flex-col gap-y-4 items-center justify-center h-32">
          <Search className="size-6 text-muted-foreground" />
          <p className="text-muted-foreground text-sm">
            No projects found
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <ConfirmDialog />
      <h3 className="font-semibold text-lg">
        Recent projects
      </h3>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {data.pages.map((group, i) => (
          <React.Fragment key={i}>
            {group.data.map((project) => (
              <ProjectCard
                key={project.id}
                id={project.id}
                name={project.name}
                width={project.width}
                height={project.height}
                thumbnailUrl={project.thumbnailUrl}
                updatedAt={new Date(project.updatedAt)}
                isPublic={project.isPublic || false}
                isCustomizable={project.isCustomizable || false}
                onClick={() => router.push(`/editor/${project.id}`)}
                onCopy={onCopy}
                onDelete={onDelete}
                onTogglePublic={onTogglePublic}
                onConfigureTemplate={onConfigureTemplate}
                copyLoading={duplicateMutation.isPending}
                deleteLoading={removeMutation.isPending}
                togglePublicLoading={updateMutation.isPending && updatingProjectId === project.id}
              />
            ))}
          </React.Fragment>
        ))}
      </div>
      {hasNextPage && (
        <div className="w-full flex items-center justify-center pt-4">
          <Button
            variant="ghost"
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage}
          >
            {isFetchingNextPage ? (
              <>
                <Loader className="size-4 mr-2 animate-spin" />
                Loading...
              </>
            ) : (
              "Load more"
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

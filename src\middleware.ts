import { NextRequest, NextResponse } from "next/server";

// Simple middleware without database dependencies
// This avoids importing auth config that uses database connections
export function middleware(request: NextRequest) {
  // For now, just pass through all requests
  // Auth will be handled by the API routes and server components
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
